/**
 * ACR Direct - Maskable Icon Creator
 * 
 * This script provides the exact specifications needed to create proper maskable icons
 * that will fix the cropping/zooming issue on home screens.
 * 
 * Usage: Run this in a browser console or Node.js environment to get the specifications
 */

const MASKABLE_ICON_SPECS = {
  "192x192": {
    canvasSize: { width: 192, height: 192 },
    safeZone: { 
      x: 19, 
      y: 19, 
      width: 154, 
      height: 154 
    },
    logoMaxSize: { width: 100, height: 100 },
    logoPosition: { x: 46, y: 46 },
    backgroundColor: "#ffffff",
    filename: "icon-maskable-192x192.png"
  },
  "512x512": {
    canvasSize: { width: 512, height: 512 },
    safeZone: { 
      x: 51, 
      y: 51, 
      width: 410, 
      height: 410 
    },
    logoMaxSize: { width: 270, height: 270 },
    logoPosition: { x: 121, y: 121 },
    backgroundColor: "#ffffff",
    filename: "icon-maskable-512x512.png"
  }
};

/**
 * Generate HTML5 Canvas code for creating maskable icons
 */
function generateCanvasCode(size) {
  const spec = MASKABLE_ICON_SPECS[size];
  
  return `
// Create ${size} maskable icon
const canvas${size.replace('x', '_')} = document.createElement('canvas');
canvas${size.replace('x', '_')}.width = ${spec.canvasSize.width};
canvas${size.replace('x', '_')}.height = ${spec.canvasSize.height};
const ctx${size.replace('x', '_')} = canvas${size.replace('x', '_')}.getContext('2d');

// Fill background
ctx${size.replace('x', '_')}.fillStyle = '${spec.backgroundColor}';
ctx${size.replace('x', '_')}.fillRect(0, 0, ${spec.canvasSize.width}, ${spec.canvasSize.height});

// Draw safe zone guide (remove in final version)
ctx${size.replace('x', '_')}.strokeStyle = '#0d47a1';
ctx${size.replace('x', '_')}.setLineDash([5, 5]);
ctx${size.replace('x', '_')}.strokeRect(${spec.safeZone.x}, ${spec.safeZone.y}, ${spec.safeZone.width}, ${spec.safeZone.height});

// Load and draw your logo here
// const logo = new Image();
// logo.onload = function() {
//   ctx${size.replace('x', '_')}.drawImage(logo, ${spec.logoPosition.x}, ${spec.logoPosition.y}, ${spec.logoMaxSize.width}, ${spec.logoMaxSize.height});
//   
//   // Download the result
//   const link = document.createElement('a');
//   link.download = '${spec.filename}';
//   link.href = canvas${size.replace('x', '_')}.toDataURL();
//   link.click();
// };
// logo.src = '/android-chrome-${size}.png'; // Your source logo
`;
}

/**
 * Generate CSS for visual preview
 */
function generatePreviewCSS() {
  return `
.maskable-preview {
  display: flex;
  gap: 20px;
  margin: 20px 0;
}

.icon-preview {
  text-align: center;
}

.icon-container {
  width: 100px;
  height: 100px;
  margin: 10px auto;
  position: relative;
  background: white;
  border: 2px solid #ddd;
  border-radius: 8px;
}

.mask-circle {
  border-radius: 50%;
  overflow: hidden;
}

.mask-rounded {
  border-radius: 25%;
  overflow: hidden;
}

.mask-square {
  border-radius: 0;
  overflow: hidden;
}

.logo-placeholder {
  width: 60px;
  height: 60px;
  background: #0d47a1;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 12px;
  margin: 20px auto;
  border-radius: 4px;
}
`;
}

/**
 * Generate complete HTML tool for creating maskable icons
 */
function generateHTMLTool() {
  return `
<!DOCTYPE html>
<html>
<head>
    <title>ACR Direct - Maskable Icon Creator</title>
    <style>${generatePreviewCSS()}</style>
</head>
<body>
    <h1>ACR Direct - Maskable Icon Creator</h1>
    
    <div class="maskable-preview">
        <div class="icon-preview">
            <h3>Circle Mask</h3>
            <div class="icon-container mask-circle">
                <div class="logo-placeholder">ACR</div>
            </div>
        </div>
        <div class="icon-preview">
            <h3>Rounded Mask</h3>
            <div class="icon-container mask-rounded">
                <div class="logo-placeholder">ACR</div>
            </div>
        </div>
        <div class="icon-preview">
            <h3>Square Mask</h3>
            <div class="icon-container mask-square">
                <div class="logo-placeholder">ACR</div>
            </div>
        </div>
    </div>
    
    <button onclick="createMaskableIcons()">Create Maskable Icons</button>
    
    <script>
        ${generateCanvasCode("192x192")}
        ${generateCanvasCode("512x512")}
        
        function createMaskableIcons() {
            console.log("Creating maskable icons...");
            console.log("192x192 specifications:", ${JSON.stringify(MASKABLE_ICON_SPECS["192x192"], null, 2)});
            console.log("512x512 specifications:", ${JSON.stringify(MASKABLE_ICON_SPECS["512x512"], null, 2)});
            
            // Execute canvas code here
            eval(canvas192_192Code);
            eval(canvas512_512Code);
        }
    </script>
</body>
</html>
`;
}

/**
 * Main function to display all specifications
 */
function displaySpecifications() {
  console.log("=== ACR Direct Maskable Icon Specifications ===");
  console.log("");
  
  Object.keys(MASKABLE_ICON_SPECS).forEach(size => {
    const spec = MASKABLE_ICON_SPECS[size];
    console.log(`${size} Maskable Icon:`);
    console.log(`  Canvas: ${spec.canvasSize.width}x${spec.canvasSize.height}px`);
    console.log(`  Safe Zone: ${spec.safeZone.width}x${spec.safeZone.height}px at (${spec.safeZone.x}, ${spec.safeZone.y})`);
    console.log(`  Logo Max: ${spec.logoMaxSize.width}x${spec.logoMaxSize.height}px at (${spec.logoPosition.x}, ${spec.logoPosition.y})`);
    console.log(`  Background: ${spec.backgroundColor}`);
    console.log(`  Filename: ${spec.filename}`);
    console.log("");
  });
  
  console.log("=== Implementation Steps ===");
  console.log("1. Create icons with the specifications above");
  console.log("2. Place ACR Direct logo centered in the safe zone");
  console.log("3. Use white background that fills entire canvas");
  console.log("4. Save as PNG files in public/ directory");
  console.log("5. Test with https://maskable.app/");
  console.log("");
  
  return MASKABLE_ICON_SPECS;
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    MASKABLE_ICON_SPECS,
    generateCanvasCode,
    generatePreviewCSS,
    generateHTMLTool,
    displaySpecifications
  };
}

// Auto-run if in browser
if (typeof window !== 'undefined') {
  displaySpecifications();
}
