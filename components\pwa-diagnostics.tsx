"use client"

import { useState, useEffect } from "react"
import { usePWAInstall } from "@/lib/hooks/use-pwa-install"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { CheckCircle, XCircle, AlertCircle, RefreshCw, Download, Smartphone, Monitor } from "lucide-react"

export function PWADiagnostics() {
  const { 
    capabilities, 
    isInstallable, 
    isInstalled, 
    installationDismissed,
    promptInstall,
    resetInstallationState
  } = usePWAInstall()
  
  const [serviceWorkerStatus, setServiceWorkerStatus] = useState<string>("Checking...")
  const [manifestStatus, setManifestStatus] = useState<string>("Checking...")

  useEffect(() => {
    // Vérifier le service worker
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistration().then(registration => {
        if (registration) {
          setServiceWorkerStatus(`Active (${registration.scope})`)
        } else {
          setServiceWorkerStatus("Not registered")
        }
      }).catch(() => {
        setServiceWorkerStatus("Error checking")
      })
    } else {
      setServiceWorkerStatus("Not supported")
    }

    // Vérifier le manifest
    fetch('/manifest.json')
      .then(response => {
        if (response.ok) {
          setManifestStatus("Available")
        } else {
          setManifestStatus(`Error: ${response.status}`)
        }
      })
      .catch(() => {
        setManifestStatus("Not found")
      })
  }, [])

  const getStatusIcon = (status: boolean | string) => {
    if (typeof status === 'boolean') {
      return status ? <CheckCircle className="h-4 w-4 text-green-500" /> : <XCircle className="h-4 w-4 text-red-500" />
    }
    if (status.includes("Error") || status.includes("Not")) {
      return <XCircle className="h-4 w-4 text-red-500" />
    }
    return <CheckCircle className="h-4 w-4 text-green-500" />
  }

  const getStatusBadge = (status: boolean) => {
    return status ? (
      <Badge variant="default" className="bg-green-100 text-green-800">Oui</Badge>
    ) : (
      <Badge variant="secondary" className="bg-red-100 text-red-800">Non</Badge>
    )
  }

  const getPlatformIcon = () => {
    switch (capabilities.platform) {
      case "ios":
        return <Smartphone className="h-4 w-4" />
      case "android":
        return <Smartphone className="h-4 w-4" />
      case "windows":
      case "macos":
      case "linux":
        return <Monitor className="h-4 w-4" />
      default:
        return <AlertCircle className="h-4 w-4" />
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Diagnostic PWA
          </CardTitle>
          <CardDescription>
            État actuel de l'installation et des capacités PWA
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* État général */}
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Application installée</span>
              {getStatusBadge(isInstalled)}
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Installation possible</span>
              {getStatusBadge(isInstallable)}
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Installation refusée</span>
              {getStatusBadge(installationDismissed)}
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Support PWA</span>
              {getStatusBadge(capabilities.supportsPWA)}
            </div>
          </div>

          <Separator />

          {/* Détails de la plateforme */}
          <div className="space-y-3">
            <h4 className="text-sm font-semibold flex items-center gap-2">
              {getPlatformIcon()}
              Détails de la plateforme
            </h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Plateforme:</span>
                <Badge variant="outline" className="ml-2">{capabilities.platform}</Badge>
              </div>
              <div>
                <span className="font-medium">Navigateur:</span>
                <Badge variant="outline" className="ml-2">{capabilities.browserType}</Badge>
              </div>
              <div>
                <span className="font-medium">Méthode d'installation:</span>
                <Badge 
                  variant="outline" 
                  className={`ml-2 ${
                    capabilities.installMethod === 'native' ? 'bg-green-50 text-green-700' :
                    capabilities.installMethod === 'manual' ? 'bg-yellow-50 text-yellow-700' :
                    'bg-red-50 text-red-700'
                  }`}
                >
                  {capabilities.installMethod}
                </Badge>
              </div>
              <div>
                <span className="font-medium">Mode standalone:</span>
                {getStatusBadge(capabilities.isStandalone)}
              </div>
            </div>
          </div>

          <Separator />

          {/* État technique */}
          <div className="space-y-3">
            <h4 className="text-sm font-semibold">État technique</h4>
            <div className="space-y-2 text-sm">
              <div className="flex items-center justify-between">
                <span>Service Worker</span>
                <div className="flex items-center gap-2">
                  {getStatusIcon(serviceWorkerStatus)}
                  <span className="text-xs text-muted-foreground">{serviceWorkerStatus}</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span>Manifest</span>
                <div className="flex items-center gap-2">
                  {getStatusIcon(manifestStatus)}
                  <span className="text-xs text-muted-foreground">{manifestStatus}</span>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Actions */}
          <div className="flex flex-wrap gap-2">
            <Button 
              onClick={promptInstall} 
              disabled={!isInstallable || isInstalled}
              size="sm"
            >
              <Download className="h-4 w-4 mr-2" />
              Tester l'installation
            </Button>
            <Button 
              onClick={resetInstallationState} 
              variant="outline"
              size="sm"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Réinitialiser l'état
            </Button>
            <Button 
              onClick={() => window.location.reload()} 
              variant="outline"
              size="sm"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Recharger la page
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
