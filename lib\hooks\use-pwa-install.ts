"use client"

import { useState, useEffect, useRef, useCallback } from "react"

type BeforeInstallPromptEvent = Event & {
  prompt: () => Promise<void>
  userChoice: Promise<{ outcome: "accepted" | "dismissed"; platform: string }>
}

interface PWACapabilities {
  canInstall: boolean
  isInstalled: boolean
  isStandalone: boolean
  supportsPWA: boolean
  browserType: "chrome" | "edge" | "firefox" | "safari" | "other"
  platform: "ios" | "android" | "windows" | "macos" | "linux" | "other"
  installMethod: "native" | "manual" | "unsupported"
}

export function usePWAInstall() {
  const [installPrompt, setInstallPrompt] = useState<BeforeInstallPromptEvent | null>(null)
  const [isInstalled, setIsInstalled] = useState<boolean>(false)
  const [isIOS, setIsIOS] = useState<boolean>(false)
  const [isWindows, setIsWindows] = useState<boolean>(false)
  const [showIOSGuide, setShowIOSGuide] = useState<boolean>(false)
  const [capabilities, setCapabilities] = useState<PWACapabilities>({
    canInstall: false,
    isInstalled: false,
    isStandalone: false,
    supportsPWA: false,
    browserType: "other",
    platform: "other",
    installMethod: "unsupported"
  })
  const [installationDismissed, setInstallationDismissed] = useState<boolean>(false)
  const promptEventRef = useRef<BeforeInstallPromptEvent | null>(null)

  // Détecter les capacités du navigateur et de la plateforme
  const detectCapabilities = useCallback((): PWACapabilities => {
    if (typeof window === "undefined") {
      return {
        canInstall: false,
        isInstalled: false,
        isStandalone: false,
        supportsPWA: false,
        browserType: "other",
        platform: "other",
        installMethod: "unsupported"
      }
    }

    const userAgent = navigator.userAgent.toLowerCase()

    // Détecter le navigateur
    let browserType: PWACapabilities["browserType"] = "other"
    if (/chrome/.test(userAgent) && !/edge|edg/.test(userAgent)) {
      browserType = "chrome"
    } else if (/edge|edg/.test(userAgent)) {
      browserType = "edge"
    } else if (/firefox/.test(userAgent)) {
      browserType = "firefox"
    } else if (/safari/.test(userAgent) && !/chrome/.test(userAgent)) {
      browserType = "safari"
    }

    // Détecter la plateforme
    let platform: PWACapabilities["platform"] = "other"
    if (/ipad|iphone|ipod/.test(userAgent) && !(window as any).MSStream) {
      platform = "ios"
    } else if (/android/.test(userAgent)) {
      platform = "android"
    } else if (/windows/.test(userAgent)) {
      platform = "windows"
    } else if (/macintosh|mac os x/.test(userAgent)) {
      platform = "macos"
    } else if (/linux/.test(userAgent)) {
      platform = "linux"
    }

    // Vérifier si l'app est en mode standalone
    const isStandalone =
      window.matchMedia("(display-mode: standalone)").matches ||
      window.matchMedia("(display-mode: fullscreen)").matches ||
      window.matchMedia("(display-mode: minimal-ui)").matches ||
      (window.navigator as any).standalone === true

    // Vérifier si l'app est installée
    const isInstalled = isStandalone || localStorage.getItem("pwa-installed") === "true"

    // Vérifier le support PWA
    const supportsPWA =
      "serviceWorker" in navigator &&
      (browserType === "chrome" || browserType === "edge" ||
       (browserType === "safari" && platform === "ios") ||
       (browserType === "firefox" && "onbeforeinstallprompt" in window))

    // Déterminer la méthode d'installation
    let installMethod: PWACapabilities["installMethod"] = "unsupported"
    if (platform === "ios" && browserType === "safari") {
      installMethod = "manual"
    } else if (supportsPWA && (browserType === "chrome" || browserType === "edge")) {
      installMethod = "native"
    } else if (browserType === "firefox") {
      installMethod = "unsupported"
    }

    // Vérifier si l'installation est possible
    const canInstall = supportsPWA && !isInstalled && installMethod !== "unsupported"

    return {
      canInstall,
      isInstalled,
      isStandalone,
      supportsPWA,
      browserType,
      platform,
      installMethod
    }
  }, [])

  useEffect(() => {
    if (typeof window === "undefined") return

    // Détecter les capacités initiales
    const caps = detectCapabilities()
    setCapabilities(caps)
    setIsInstalled(caps.isInstalled)
    setIsIOS(caps.platform === "ios")
    setIsWindows(caps.platform === "windows")

    // Vérifier si l'installation a été précédemment refusée
    const dismissed = localStorage.getItem("pwa-install-dismissed")
    const dismissedDate = localStorage.getItem("pwa-install-dismissed-date")

    // Réinitialiser le refus après 7 jours
    if (dismissed && dismissedDate) {
      const daysSinceDismissed = (Date.now() - new Date(dismissedDate).getTime()) / (1000 * 60 * 60 * 24)
      if (daysSinceDismissed > 7) {
        localStorage.removeItem("pwa-install-dismissed")
        localStorage.removeItem("pwa-install-dismissed-date")
        setInstallationDismissed(false)
      } else {
        setInstallationDismissed(true)
      }
    }

    console.log("PWA Capabilities detected:", caps)

    if (!caps.isInstalled && caps.supportsPWA) {
      // Gérer l'événement beforeinstallprompt
      const handleBeforeInstallPrompt = (e: Event) => {
        e.preventDefault()
        const promptEvent = e as BeforeInstallPromptEvent
        setInstallPrompt(promptEvent)
        promptEventRef.current = promptEvent
        console.log("Événement beforeinstallprompt capturé avec succès")
      }

      console.log("Ajout de l'écouteur d'événement beforeinstallprompt")
      window.addEventListener("beforeinstallprompt", handleBeforeInstallPrompt)

      // Vérifier à nouveau après l'installation
      window.addEventListener("appinstalled", () => {
        console.log("Application installée avec succès")
        const newCaps = detectCapabilities()
        setCapabilities(newCaps)
        setIsInstalled(true)
        setInstallPrompt(null)
        promptEventRef.current = null

        // Stocker l'état d'installation
        localStorage.setItem("pwa-installed", "true")
        localStorage.setItem("pwa-install-date", new Date().toISOString())

        // Nettoyer les préférences de refus
        localStorage.removeItem("pwa-install-dismissed")
        localStorage.removeItem("pwa-install-dismissed-date")
      })

      return () => {
        window.removeEventListener("beforeinstallprompt", handleBeforeInstallPrompt)
        window.removeEventListener("appinstalled", () => {})
      }
    }
  }, [detectCapabilities])

  // Fonction pour déclencher l'installation
  const promptInstall = async () => {
    console.log("Tentative d'installation:", {
      capabilities,
      hasInstallPrompt: !!installPrompt,
    })

    // Vérifier si l'app est déjà installée
    if (capabilities.isInstalled) {
      console.log("Application déjà installée")
      return
    }

    // iOS Safari - Guide manuel
    if (capabilities.platform === "ios" && capabilities.browserType === "safari") {
      console.log("Affichage du guide iOS")
      setShowIOSGuide(true)
      return
    }

    // Installation native (Chrome, Edge)
    if (installPrompt && capabilities.installMethod === "native") {
      try {
        console.log("Déclenchement du prompt d'installation natif")
        await installPrompt.prompt()
        const choiceResult = await installPrompt.userChoice

        if (choiceResult.outcome === "accepted") {
          console.log("Utilisateur a accepté l'installation")
          setInstallPrompt(null)
          promptEventRef.current = null
        } else {
          console.log("Utilisateur a refusé l'installation")
          // Marquer comme refusé temporairement
          localStorage.setItem("pwa-install-dismissed", "true")
          localStorage.setItem("pwa-install-dismissed-date", new Date().toISOString())
          setInstallationDismissed(true)
        }
      } catch (error) {
        console.error("Erreur lors de l'installation:", error)
        // Fallback vers le guide manuel si disponible
        if (capabilities.platform === "windows") {
          showWindowsInstallGuide()
        }
      }
    } else {
      console.log("Prompt d'installation non disponible, utilisation du guide manuel")

      // Fallback vers les guides manuels
      if (capabilities.platform === "ios") {
        setShowIOSGuide(true)
      } else if (capabilities.platform === "windows") {
        showWindowsInstallGuide()
      } else {
        // Navigateur non supporté
        alert("Votre navigateur ne prend pas en charge l'installation de cette application. Veuillez utiliser Chrome, Edge ou Safari.")
      }
    }
  }

  // Fonction pour afficher un guide d'installation pour Windows
  const showWindowsInstallGuide = () => {
    // Détecter le navigateur
    const isChrome =
      /chrome/.test(navigator.userAgent.toLowerCase()) && !/edge|edg/.test(navigator.userAgent.toLowerCase())
    const isEdge = /edge|edg/.test(navigator.userAgent.toLowerCase())
    const isFirefox = /firefox/.test(navigator.userAgent.toLowerCase())

    let message = "Pour installer cette application sur Windows:\n\n"

    if (isChrome) {
      message += "1. Cliquez sur les trois points (...) en haut à droite\n"
      message += "2. Sélectionnez 'Installer ACR Direct...'\n"
      message += "3. Suivez les instructions à l'écran"
    } else if (isEdge) {
      message += "1. Cliquez sur les trois points (...) en haut à droite\n"
      message += "2. Sélectionnez 'Applications' puis 'Installer cette application'\n"
      message += "3. Suivez les instructions à l'écran"
    } else if (isFirefox) {
      message += "Firefox ne prend pas encore en charge l'installation de PWA. Veuillez utiliser Chrome ou Edge."
    } else {
      message += "Veuillez utiliser Chrome ou Edge pour installer cette application."
    }

    alert(message)
  }

  // Fermer le guide iOS
  const closeIOSGuide = () => {
    setShowIOSGuide(false)
  }

  // Fonction pour refuser définitivement l'installation
  const dismissInstallation = () => {
    localStorage.setItem("pwa-install-dismissed", "true")
    localStorage.setItem("pwa-install-dismissed-date", new Date().toISOString())
    setInstallationDismissed(true)
  }

  // Fonction pour réinitialiser l'état d'installation
  const resetInstallationState = () => {
    localStorage.removeItem("pwa-installed")
    localStorage.removeItem("pwa-install-date")
    localStorage.removeItem("pwa-install-dismissed")
    localStorage.removeItem("pwa-install-dismissed-date")
    setIsInstalled(false)
    setInstallationDismissed(false)
    const newCaps = detectCapabilities()
    setCapabilities(newCaps)
  }

  return {
    // États de base (compatibilité)
    isInstallable: (!!installPrompt || capabilities.installMethod === "manual") && !capabilities.isInstalled && !installationDismissed,
    isInstalled: capabilities.isInstalled,
    isIOS: capabilities.platform === "ios",
    isWindows: capabilities.platform === "windows",
    showIOSGuide,

    // Nouvelles capacités étendues
    capabilities,
    installationDismissed,

    // Fonctions
    promptInstall,
    closeIOSGuide,
    dismissInstallation,
    resetInstallationState,
  }
}
