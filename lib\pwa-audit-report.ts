export interface PWAAuditResult {
  category: string
  name: string
  status: "pass" | "fail" | "warning" | "info"
  score: number
  message: string
  details?: string
  recommendation?: string
}

export interface PWAAuditReport {
  overallScore: number
  timestamp: Date
  results: PWAAuditResult[]
  summary: {
    passed: number
    failed: number
    warnings: number
    total: number
  }
}

export async function generatePWAAuditReport(): Promise<PWAAuditReport> {
  const results: PWAAuditResult[] = []
  
  // 1. Service Worker Audit
  try {
    if ('serviceWorker' in navigator) {
      const registration = await navigator.serviceWorker.getRegistration()
      if (registration && registration.active) {
        results.push({
          category: "Service Worker",
          name: "Service Worker Registration",
          status: "pass",
          score: 100,
          message: "Service Worker correctement enregistré et actif",
          details: `Scope: ${registration.scope}`
        })
      } else {
        results.push({
          category: "Service Worker",
          name: "Service Worker Registration",
          status: "fail",
          score: 0,
          message: "Service Worker non enregistré ou inactif",
          recommendation: "Vérifiez l'enregistrement du Service Worker dans votre application"
        })
      }
    } else {
      results.push({
        category: "Service Worker",
        name: "Service Worker Support",
        status: "fail",
        score: 0,
        message: "Service Worker non supporté par le navigateur",
        recommendation: "Utilisez un navigateur moderne supportant les Service Workers"
      })
    }
  } catch (error) {
    results.push({
      category: "Service Worker",
      name: "Service Worker Check",
      status: "fail",
      score: 0,
      message: "Erreur lors de la vérification du Service Worker",
      details: error instanceof Error ? error.message : "Erreur inconnue"
    })
  }

  // 2. Manifest Audit
  try {
    const response = await fetch('/manifest.json')
    if (response.ok) {
      const manifest = await response.json()
      
      // Vérifier les propriétés essentielles
      const requiredFields = ['name', 'short_name', 'start_url', 'display', 'icons']
      const missingFields = requiredFields.filter(field => !manifest[field])
      
      if (missingFields.length === 0) {
        results.push({
          category: "Manifest",
          name: "Manifest Validity",
          status: "pass",
          score: 100,
          message: "Manifest valide avec toutes les propriétés requises",
          details: `Nom: ${manifest.name}, Display: ${manifest.display}`
        })
      } else {
        results.push({
          category: "Manifest",
          name: "Manifest Validity",
          status: "warning",
          score: 70,
          message: "Manifest présent mais propriétés manquantes",
          details: `Propriétés manquantes: ${missingFields.join(', ')}`,
          recommendation: "Ajoutez les propriétés manquantes au manifest.json"
        })
      }

      // Vérifier les icônes
      if (manifest.icons && manifest.icons.length > 0) {
        const hasRequiredSizes = manifest.icons.some((icon: any) => 
          icon.sizes === "192x192" || icon.sizes === "512x512"
        )
        
        if (hasRequiredSizes) {
          results.push({
            category: "Manifest",
            name: "Icons",
            status: "pass",
            score: 100,
            message: "Icônes PWA présentes avec les tailles requises"
          })
        } else {
          results.push({
            category: "Manifest",
            name: "Icons",
            status: "warning",
            score: 60,
            message: "Icônes présentes mais tailles recommandées manquantes",
            recommendation: "Ajoutez des icônes 192x192 et 512x512 pixels"
          })
        }
      } else {
        results.push({
          category: "Manifest",
          name: "Icons",
          status: "fail",
          score: 0,
          message: "Aucune icône définie dans le manifest",
          recommendation: "Ajoutez des icônes PWA au manifest.json"
        })
      }
    } else {
      results.push({
        category: "Manifest",
        name: "Manifest Accessibility",
        status: "fail",
        score: 0,
        message: `Manifest inaccessible (HTTP ${response.status})`,
        recommendation: "Vérifiez que le fichier manifest.json est accessible"
      })
    }
  } catch (error) {
    results.push({
      category: "Manifest",
      name: "Manifest Check",
      status: "fail",
      score: 0,
      message: "Erreur lors de la vérification du manifest",
      details: error instanceof Error ? error.message : "Erreur inconnue"
    })
  }

  // 3. HTTPS/Security Audit
  results.push({
    category: "Security",
    name: "Secure Context",
    status: window.isSecureContext ? "pass" : "fail",
    score: window.isSecureContext ? 100 : 0,
    message: window.isSecureContext ? "Application servie en contexte sécurisé" : "Application non servie en HTTPS",
    details: `Protocol: ${window.location.protocol}`,
    recommendation: window.isSecureContext ? undefined : "Servez votre application en HTTPS"
  })

  // 4. Installation Audit
  const userAgent = navigator.userAgent.toLowerCase()
  const isIOS = /ipad|iphone|ipod/.test(userAgent)
  const isAndroid = /android/.test(userAgent)
  const isChrome = /chrome/.test(userAgent) && !/edge|edg/.test(userAgent)
  const isEdge = /edge|edg/.test(userAgent)

  const supportsInstallation = isChrome || isEdge || isIOS || isAndroid
  
  results.push({
    category: "Installation",
    name: "Installation Support",
    status: supportsInstallation ? "pass" : "warning",
    score: supportsInstallation ? 100 : 50,
    message: supportsInstallation ? "Installation PWA supportée" : "Support d'installation limité",
    details: `Navigateur: ${isChrome ? 'Chrome' : isEdge ? 'Edge' : isIOS ? 'Safari iOS' : 'Autre'}`,
    recommendation: supportsInstallation ? undefined : "Utilisez Chrome, Edge ou Safari pour une meilleure expérience d'installation"
  })

  // 5. Performance Audit
  const navigationTiming = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
  const loadTime = navigationTiming ? navigationTiming.loadEventEnd - navigationTiming.fetchStart : 0

  let performanceStatus: "pass" | "warning" | "fail" = "pass"
  let performanceScore = 100
  
  if (loadTime > 3000) {
    performanceStatus = "fail"
    performanceScore = 30
  } else if (loadTime > 2000) {
    performanceStatus = "warning"
    performanceScore = 70
  }

  results.push({
    category: "Performance",
    name: "Load Time",
    status: performanceStatus,
    score: performanceScore,
    message: `Temps de chargement: ${Math.round(loadTime)}ms`,
    recommendation: loadTime > 2000 ? "Optimisez les performances de chargement" : undefined
  })

  // 6. Offline Capability Audit
  try {
    if ('caches' in window) {
      const cacheNames = await caches.keys()
      const hasCaches = cacheNames.length > 0
      
      results.push({
        category: "Offline",
        name: "Cache Storage",
        status: hasCaches ? "pass" : "warning",
        score: hasCaches ? 100 : 30,
        message: hasCaches ? `${cacheNames.length} cache(s) disponible(s)` : "Aucun cache détecté",
        details: hasCaches ? `Caches: ${cacheNames.join(', ')}` : undefined,
        recommendation: hasCaches ? undefined : "Implémentez une stratégie de mise en cache"
      })
    } else {
      results.push({
        category: "Offline",
        name: "Cache API Support",
        status: "fail",
        score: 0,
        message: "Cache API non supporté",
        recommendation: "Utilisez un navigateur supportant l'API Cache"
      })
    }
  } catch (error) {
    results.push({
      category: "Offline",
      name: "Cache Check",
      status: "fail",
      score: 0,
      message: "Erreur lors de la vérification du cache",
      details: error instanceof Error ? error.message : "Erreur inconnue"
    })
  }

  // Calculer le score global et le résumé
  const totalScore = results.reduce((sum, result) => sum + result.score, 0)
  const overallScore = Math.round(totalScore / results.length)
  
  const summary = {
    passed: results.filter(r => r.status === "pass").length,
    failed: results.filter(r => r.status === "fail").length,
    warnings: results.filter(r => r.status === "warning").length,
    total: results.length
  }

  return {
    overallScore,
    timestamp: new Date(),
    results,
    summary
  }
}

export function exportAuditReport(report: PWAAuditReport): string {
  const lines = [
    "# Rapport d'audit PWA - ACR Direct",
    `Généré le: ${report.timestamp.toLocaleString()}`,
    `Score global: ${report.overallScore}/100`,
    "",
    "## Résumé",
    `- ✅ Tests réussis: ${report.summary.passed}`,
    `- ❌ Tests échoués: ${report.summary.failed}`,
    `- ⚠️ Avertissements: ${report.summary.warnings}`,
    `- 📊 Total: ${report.summary.total}`,
    "",
    "## Détails des tests",
    ""
  ]

  report.results.forEach(result => {
    const icon = result.status === "pass" ? "✅" : result.status === "fail" ? "❌" : "⚠️"
    lines.push(`### ${icon} ${result.category} - ${result.name}`)
    lines.push(`**Score:** ${result.score}/100`)
    lines.push(`**Message:** ${result.message}`)
    
    if (result.details) {
      lines.push(`**Détails:** ${result.details}`)
    }
    
    if (result.recommendation) {
      lines.push(`**Recommandation:** ${result.recommendation}`)
    }
    
    lines.push("")
  })

  return lines.join("\n")
}
