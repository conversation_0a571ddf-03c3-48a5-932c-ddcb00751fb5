"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  FileText, 
  Download, 
  Play, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Clock,
  TrendingUp,
  RefreshCw
} from "lucide-react"
import { generatePWAAuditReport, exportAuditReport, type PWAAuditReport } from "@/lib/pwa-audit-report"

export function PWAAuditReportComponent() {
  const [report, setReport] = useState<PWAAuditReport | null>(null)
  const [isGenerating, setIsGenerating] = useState(false)
  const [lastGenerated, setLastGenerated] = useState<Date | null>(null)

  const runAudit = async () => {
    setIsGenerating(true)
    try {
      const auditReport = await generatePWAAuditReport()
      setReport(auditReport)
      setLastGenerated(new Date())
    } catch (error) {
      console.error("Erreur lors de la génération du rapport d'audit:", error)
    } finally {
      setIsGenerating(false)
    }
  }

  const downloadReport = () => {
    if (!report) return

    const reportText = exportAuditReport(report)
    const blob = new Blob([reportText], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `pwa-audit-report-${new Date().toISOString().split('T')[0]}.md`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pass":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "fail":
        return <XCircle className="h-4 w-4 text-red-500" />
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      pass: "bg-green-100 text-green-800",
      fail: "bg-red-100 text-red-800",
      warning: "bg-yellow-100 text-yellow-800",
      info: "bg-blue-100 text-blue-800"
    }
    
    return (
      <Badge className={variants[status as keyof typeof variants] || variants.info}>
        {status === "pass" ? "Réussi" : status === "fail" ? "Échec" : status === "warning" ? "Attention" : "Info"}
      </Badge>
    )
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600"
    if (score >= 70) return "text-yellow-600"
    return "text-red-600"
  }

  const getScoreBadge = (score: number) => {
    if (score >= 90) return "bg-green-100 text-green-800"
    if (score >= 70) return "bg-yellow-100 text-yellow-800"
    return "bg-red-100 text-red-800"
  }

  const groupedResults = report?.results.reduce((acc, result) => {
    if (!acc[result.category]) {
      acc[result.category] = []
    }
    acc[result.category].push(result)
    return acc
  }, {} as Record<string, typeof report.results>) || {}

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Rapport d'audit PWA
        </CardTitle>
        <CardDescription>
          Analyse complète de la conformité et des performances PWA
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Contrôles */}
        <div className="flex items-center justify-between">
          <div className="flex gap-2">
            <Button onClick={runAudit} disabled={isGenerating}>
              {isGenerating ? <RefreshCw className="h-4 w-4 animate-spin mr-2" /> : <Play className="h-4 w-4 mr-2" />}
              {isGenerating ? "Génération..." : "Générer le rapport"}
            </Button>
            {report && (
              <Button onClick={downloadReport} variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Télécharger
              </Button>
            )}
          </div>
          {lastGenerated && (
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Clock className="h-3 w-3" />
              {lastGenerated.toLocaleTimeString()}
            </div>
          )}
        </div>

        {/* Score global */}
        {report && (
          <>
            <div className="text-center space-y-4">
              <div className={`text-5xl font-bold ${getScoreColor(report.overallScore)}`}>
                {report.overallScore}/100
              </div>
              <Badge className={getScoreBadge(report.overallScore)}>
                {report.overallScore >= 90 ? "Excellent" : 
                 report.overallScore >= 70 ? "Bon" : 
                 report.overallScore >= 50 ? "Moyen" : "À améliorer"}
              </Badge>
              <Progress value={report.overallScore} className="w-full" />
            </div>

            <Separator />

            {/* Résumé */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center space-y-1">
                <div className="text-2xl font-bold text-green-600">{report.summary.passed}</div>
                <div className="text-xs text-muted-foreground">Réussis</div>
              </div>
              <div className="text-center space-y-1">
                <div className="text-2xl font-bold text-red-600">{report.summary.failed}</div>
                <div className="text-xs text-muted-foreground">Échecs</div>
              </div>
              <div className="text-center space-y-1">
                <div className="text-2xl font-bold text-yellow-600">{report.summary.warnings}</div>
                <div className="text-xs text-muted-foreground">Avertissements</div>
              </div>
              <div className="text-center space-y-1">
                <div className="text-2xl font-bold text-blue-600">{report.summary.total}</div>
                <div className="text-xs text-muted-foreground">Total</div>
              </div>
            </div>

            <Separator />

            {/* Résultats par catégorie */}
            <div className="space-y-6">
              {Object.entries(groupedResults).map(([category, results]) => (
                <div key={category} className="space-y-3">
                  <h4 className="font-semibold flex items-center gap-2">
                    <TrendingUp className="h-4 w-4" />
                    {category}
                  </h4>
                  <div className="space-y-2">
                    {results.map((result, index) => (
                      <div key={index} className="border rounded-lg p-4 space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(result.status)}
                            <span className="font-medium">{result.name}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">{result.score}/100</Badge>
                            {getStatusBadge(result.status)}
                          </div>
                        </div>
                        <p className="text-sm text-muted-foreground">{result.message}</p>
                        {result.details && (
                          <div className="text-xs bg-muted p-2 rounded">
                            <strong>Détails:</strong> {result.details}
                          </div>
                        )}
                        {result.recommendation && (
                          <Alert>
                            <AlertTriangle className="h-4 w-4" />
                            <AlertDescription className="text-sm">
                              <strong>Recommandation:</strong> {result.recommendation}
                            </AlertDescription>
                          </Alert>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </>
        )}

        {!report && !isGenerating && (
          <div className="text-center py-12 text-muted-foreground">
            <FileText className="h-16 w-16 mx-auto mb-4 opacity-50" />
            <h3 className="text-lg font-medium mb-2">Aucun rapport généré</h3>
            <p className="text-sm">Cliquez sur "Générer le rapport" pour commencer l'audit PWA</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
