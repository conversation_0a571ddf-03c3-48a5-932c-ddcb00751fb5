"use client"

import type React from "react"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Skeleton } from "@/components/ui/skeleton" // Assurez-vous d'avoir ce composant

import { useAuth } from "@/components/auth-provider"
import { useEffect, useState } from "react"
import Image from "next/image"
// Mettre à jour l'import des icônes pour ajouter UserCog
import { Newspaper, Star, Phone, User, UserCircle } from "lucide-react"
// Ajouter l'import de Permission
import type { Permission } from "@/lib/permissions"
// Add the import for the useFavoritesStatus hook
import { useFavoritesStatus } from "@/lib/hooks/use-favorites-status"
// Ajouter l'import du bouton d'installation
import { InstallButton } from "@/components/install-button"
// Ajouter l'import du hook PWA pour vérifier si l'app est installée
import { usePWAInstall } from "@/lib/hooks/use-pwa-install"

// Mettre à jour l'interface DashboardNavProps pour inclure permissionRequired
interface DashboardNavProps {
  items?: {
    title: string
    href: string
    icon?: React.ReactNode
    iconUrl?: string | null
    permissionRequired?: string
  }[]
}

// Modifier le tableau defaultStaticMenuItems pour supprimer l'élément de profil utilisateur
// Remplacer le tableau defaultStaticMenuItems actuel par celui-ci:
const defaultStaticMenuItems = [
  {
    id: "dashboard",
    title: "Fil d'actualité",
    href: "/dashboard",
    icon: <Newspaper className="h-5 w-5" />,
  },
  {
    id: "favorites",
    title: "Mes favoris",
    href: "/dashboard/favorites",
    icon: <Star className="h-4 w-4" />,
  },
  {
    id: "commercial",
    title: "Contact commercial",
    href: "/dashboard/commercial",
    icon: <Phone className="h-4 w-4" />,
  },
  {
    id: "commercial-profile",
    title: "Mon profil commercial",
    href: "/dashboard/commercial-profile",
    icon: <User className="h-4 w-4" />,
    permissionRequired: "update:commercials",
  },
]

// Modifier la fonction DashboardNav pour filtrer les éléments en fonction des permissions

// Update the DashboardNav function to use the hook
export function DashboardNav({ items = [] }: DashboardNavProps) {
  const pathname = usePathname()
  const { user, isAdmin, menuItems, isMenuLoading, hasPermission } = useAuth()
  const { hasFavorites, isLoading: isFavoritesLoading } = useFavoritesStatus()
  const { isInstalled } = usePWAInstall()
  const [allItems, setAllItems] = useState([...defaultStaticMenuItems])

  // Combiner les éléments par défaut avec les éléments de menu dynamiques
  useEffect(() => {
    if (menuItems && menuItems.length > 0) {
      // Filter the static items based on permissions and favorites status
      const filteredStaticItems = defaultStaticMenuItems.filter((item) => {
        // Skip the favorites menu item if the user has no favorites
        if (item.id === "favorites" && hasFavorites === false) {
          return false
        }
        return !item.permissionRequired || hasPermission(item.permissionRequired as Permission)
      })

      const dynamicItems = menuItems.map((item) => ({
        title: item.title,
        href: item.path,
        iconUrl: item.iconUrl,
      }))

      setAllItems([...filteredStaticItems, ...dynamicItems])
    } else {
      // Filter the static items based on permissions and favorites status
      const filteredStaticItems = defaultStaticMenuItems.filter((item) => {
        // Skip the favorites menu item if the user has no favorites
        if (item.id === "favorites" && hasFavorites === false) {
          return false
        }
        return !item.permissionRequired || hasPermission(item.permissionRequired as Permission)
      })

      setAllItems([...filteredStaticItems])
    }
  }, [menuItems, hasPermission, hasFavorites])

  if (isMenuLoading || isFavoritesLoading) {
    return (
      <div className="w-full">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="px-1 py-1">
            <Skeleton className="h-8 w-full" />
          </div>
        ))}
      </div>
    )
  }

  return (
    <ScrollArea className="my-4 h-[calc(100vh-8rem)]">
      <div className="flex flex-col gap-2 p-2">
        {allItems.map((item, index) => {
          const isActive = pathname === item.href
          const isDashboard = item.href === "/dashboard"

          return (
            <Button
              key={item.href}
              variant={isActive ? "secondary" : "ghost"}
              size={isDashboard ? "default" : "sm"}
              className={cn(
                "w-full min-w-0 transition-all duration-300 rounded-lg",
                // Primary Level - Fil d'actualité (most prominent but balanced)
                isDashboard && [
                  "nav-primary",
                  "bg-transparent hover:bg-muted/50 border-2 border-primary hover:border-primary",
                  "text-primary font-bold text-lg",
                  "mb-3 justify-center h-12",
                  "shadow-sm hover:shadow-md"
                ].join(" "),
                // Secondary Level - Contact commercial (high importance)
                !isDashboard && item.id === "commercial" && [
                  "nav-secondary",
                  "bg-transparent hover:bg-muted/50",
                  "border border-primary hover:border-primary",
                  "text-primary font-semibold",
                  "justify-start h-11 mb-2",
                  "shadow-sm hover:shadow-md"
                ].join(" "),
                // Tertiary Level - Commercial profile (medium importance)
                !isDashboard && item.id === "commercial-profile" && [
                  "nav-tertiary",
                  "bg-transparent hover:bg-muted/50",
                  "border border-emerald-500 hover:border-emerald-600",
                  "text-emerald-600 dark:text-emerald-400 font-medium",
                  "justify-start h-10 mb-1",
                  "shadow-sm hover:shadow"
                ].join(" "),
                // Standard navigation items (dynamic pages and favorites)
                !isDashboard && !["commercial", "commercial-profile"].includes(item.id || "") && [
                  "nav-standard",
                  "bg-transparent hover:bg-muted/50 border border-muted-foreground/20 hover:border-muted-foreground/40",
                  "text-foreground font-medium",
                  "justify-start h-10",
                  "hover:shadow-sm"
                ].join(" "),
                // Active state for non-dashboard items
                isActive && !isDashboard && "ring-2 ring-primary/20 bg-primary/5",
              )}
              asChild
            >
              <Link href={item.href} className={cn("flex items-center gap-3 min-w-0 relative z-10", isDashboard && "justify-center")}>
                {item.icon && (
                  <div className={cn(
                    "flex-shrink-0 transition-all duration-300",
                    isDashboard && "text-primary",
                    !isDashboard && item.id === "commercial" && "text-primary",
                    !isDashboard && item.id === "commercial-profile" && "text-emerald-600 dark:text-emerald-400",
                    !isDashboard && !["commercial", "commercial-profile"].includes(item.id || "") && "text-muted-foreground"
                  )}>
                    {item.icon}
                  </div>
                )}
                {item.iconUrl && (
                  <div className="relative h-4 w-4 flex-shrink-0">
                    <Image
                      src={item.iconUrl || "/placeholder.svg"}
                      alt=""
                      width={16}
                      height={16}
                      className="h-4 w-4 object-contain"
                      onError={(e) => {
                        // En cas d'erreur, remplacer par une icône par défaut
                        e.currentTarget.src = "/placeholder.svg"
                      }}
                      unoptimized // Désactiver l'optimisation Next.js pour permettre le cache du service worker
                    />
                  </div>
                )}
                <span className={cn(
                  "truncate transition-all duration-300",
                  isDashboard && "text-center text-primary font-bold text-lg",
                  !isDashboard && item.id === "commercial" && "text-primary font-semibold",
                  !isDashboard && item.id === "commercial-profile" && "text-emerald-600 dark:text-emerald-400 font-medium",
                  !isDashboard && !["commercial", "commercial-profile"].includes(item.id || "") && "text-foreground font-medium"
                )}>
                  {item.title}
                </span>
              </Link>
            </Button>
          )
        })}

        {/* Utility Section - Separated from navigation */}
        <div className="mt-6 pt-4 border-t border-border/50">
          <div className="px-2">
            <p className="text-xs font-medium text-muted-foreground uppercase tracking-wider mb-3 px-2">
              Utilitaires
            </p>
            <div className="space-y-2">
              {/* Profil utilisateur */}
              <Button
                variant="ghost"
                size="sm"
                asChild
                className="w-full justify-start nav-utility bg-transparent hover:bg-muted/50 border border-transparent hover:border-muted-foreground/30 text-foreground font-medium h-10 hover:shadow-sm transition-all duration-300"
              >
                <Link href="/dashboard/profile" className="flex items-center gap-3 min-w-0">
                  <div className="flex-shrink-0 text-muted-foreground transition-all duration-300">
                    <UserCircle className="h-4 w-4" />
                  </div>
                  <span className="truncate font-medium">Mon profil</span>
                </Link>
              </Button>

              {/* Bouton d'installation (seulement si l'app n'est pas installée) */}
              <InstallButton className="w-full" />
            </div>
          </div>
        </div>
      </div>
    </ScrollArea>
  )
}
