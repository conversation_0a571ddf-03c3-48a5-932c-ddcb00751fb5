"use client"

import { Download } from "lucide-react"
import { usePWAInstall } from "@/lib/hooks/use-pwa-install"
import { Button } from "@/components/ui/button"
import { IOSInstallGuide } from "./ios-install-guide"
import { WindowsInstallGuide } from "./windows-install-guide"
import { useState } from "react"

export function InstallButton() {
  const {
    isInstallable,
    isInstalled,
    promptInstall,
    isIOS,
    isWindows,
    showIOSGuide,
    closeIOSGuide,
    capabilities,
    installationDismissed
  } = usePWAInstall()
  const [showWindowsGuide, setShowWindowsGuide] = useState(false)
  const [browserType, setBrowserType] = useState<"chrome" | "edge" | "firefox" | "other">("chrome")

  // Ne pas afficher le bouton si l'app est déjà installée ou si l'installation n'est pas supportée
  if (isInstalled || !capabilities.supportsPWA || installationDismissed) return null

  const handleInstallClick = () => {
    // Utiliser les capacités détectées pour configurer le guide Windows
    if (capabilities.platform === "windows") {
      setBrowserType(capabilities.browserType)
      setShowWindowsGuide(true)
    }

    promptInstall()
  }

  // Texte du bouton basé sur les capacités
  const getButtonText = () => {
    if (capabilities.platform === "ios") {
      return "Ajouter à l'écran d'accueil"
    } else if (capabilities.installMethod === "native") {
      return "Installer l'application"
    } else {
      return "Installer l'application"
    }
  }

  const closeWindowsGuide = () => {
    setShowWindowsGuide(false)
  }

  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        onClick={handleInstallClick}
        className="w-full justify-start hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200"
        disabled={!isInstallable}
      >
        <Download className="mr-2 h-4 w-4" />
        <span>{getButtonText()}</span>
      </Button>

      {showIOSGuide && <IOSInstallGuide onClose={closeIOSGuide} />}
      {showWindowsGuide && <WindowsInstallGuide onClose={closeWindowsGuide} browserType={browserType} />}
    </>
  )
}
