"use client"

import { useState, useEffect } from "react"
import { usePWAInstall } from "@/lib/hooks/use-pwa-install"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Play, 
  Download, 
  Smartphone, 
  Monitor,
  RefreshCw,
  TestTube
} from "lucide-react"

interface TestResult {
  name: string
  status: "pass" | "fail" | "warning" | "pending"
  message: string
  details?: string
}

export function PWATestSuite() {
  const { 
    capabilities, 
    isInstallable, 
    isInstalled, 
    installationDismissed,
    promptInstall,
    resetInstallationState
  } = usePWAInstall()
  
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [lastRun, setLastRun] = useState<Date | null>(null)

  const runTests = async () => {
    setIsRunning(true)
    const results: TestResult[] = []

    // Test 1: Service Worker
    try {
      if ('serviceWorker' in navigator) {
        const registration = await navigator.serviceWorker.getRegistration()
        if (registration) {
          results.push({
            name: "Service Worker",
            status: "pass",
            message: "Service Worker enregistré et actif",
            details: `Scope: ${registration.scope}`
          })
        } else {
          results.push({
            name: "Service Worker",
            status: "fail",
            message: "Service Worker non enregistré"
          })
        }
      } else {
        results.push({
          name: "Service Worker",
          status: "fail",
          message: "Service Worker non supporté"
        })
      }
    } catch (error) {
      results.push({
        name: "Service Worker",
        status: "fail",
        message: "Erreur lors de la vérification du Service Worker",
        details: error instanceof Error ? error.message : "Erreur inconnue"
      })
    }

    // Test 2: Manifest
    try {
      const response = await fetch('/manifest.json')
      if (response.ok) {
        const manifest = await response.json()
        results.push({
          name: "Manifest",
          status: "pass",
          message: "Manifest accessible et valide",
          details: `Nom: ${manifest.name}, Display: ${manifest.display}`
        })
      } else {
        results.push({
          name: "Manifest",
          status: "fail",
          message: `Manifest inaccessible (${response.status})`
        })
      }
    } catch (error) {
      results.push({
        name: "Manifest",
        status: "fail",
        message: "Erreur lors de la récupération du manifest",
        details: error instanceof Error ? error.message : "Erreur inconnue"
      })
    }

    // Test 3: HTTPS/Secure Context
    results.push({
      name: "Contexte sécurisé",
      status: window.isSecureContext ? "pass" : "fail",
      message: window.isSecureContext ? "Application servie en HTTPS" : "Application non servie en HTTPS",
      details: `Location: ${window.location.protocol}//${window.location.host}`
    })

    // Test 4: Capacités PWA
    results.push({
      name: "Support PWA",
      status: capabilities.supportsPWA ? "pass" : "fail",
      message: capabilities.supportsPWA ? "PWA supportée par le navigateur" : "PWA non supportée",
      details: `Navigateur: ${capabilities.browserType}, Plateforme: ${capabilities.platform}`
    })

    // Test 5: Installation
    if (capabilities.supportsPWA) {
      if (isInstalled) {
        results.push({
          name: "État d'installation",
          status: "pass",
          message: "Application installée",
          details: `Mode: ${capabilities.isStandalone ? "Standalone" : "Browser"}`
        })
      } else if (isInstallable) {
        results.push({
          name: "État d'installation",
          status: "warning",
          message: "Application installable mais non installée",
          details: `Méthode: ${capabilities.installMethod}`
        })
      } else {
        results.push({
          name: "État d'installation",
          status: "fail",
          message: "Application non installable",
          details: installationDismissed ? "Installation refusée par l'utilisateur" : "Conditions non remplies"
        })
      }
    }

    // Test 6: Icônes
    try {
      const iconTests = [
        { url: '/android-chrome-192x192.png', size: '192x192' },
        { url: '/android-chrome-512x512.png', size: '512x512' },
        { url: '/apple-touch-icon.png', size: '180x180' }
      ]

      let iconResults = []
      for (const icon of iconTests) {
        try {
          const response = await fetch(icon.url)
          iconResults.push(`${icon.size}: ${response.ok ? '✓' : '✗'}`)
        } catch {
          iconResults.push(`${icon.size}: ✗`)
        }
      }

      results.push({
        name: "Icônes PWA",
        status: iconResults.every(r => r.includes('✓')) ? "pass" : "warning",
        message: "Vérification des icônes PWA",
        details: iconResults.join(', ')
      })
    } catch (error) {
      results.push({
        name: "Icônes PWA",
        status: "fail",
        message: "Erreur lors de la vérification des icônes",
        details: error instanceof Error ? error.message : "Erreur inconnue"
      })
    }

    // Test 7: beforeinstallprompt
    results.push({
      name: "beforeinstallprompt",
      status: isInstallable && capabilities.installMethod === "native" ? "pass" : "warning",
      message: isInstallable && capabilities.installMethod === "native" 
        ? "Événement beforeinstallprompt disponible" 
        : "Événement beforeinstallprompt non disponible",
      details: `Méthode d'installation: ${capabilities.installMethod}`
    })

    setTestResults(results)
    setLastRun(new Date())
    setIsRunning(false)
  }

  const getStatusIcon = (status: TestResult["status"]) => {
    switch (status) {
      case "pass":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "fail":
        return <XCircle className="h-4 w-4 text-red-500" />
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      default:
        return <RefreshCw className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusBadge = (status: TestResult["status"]) => {
    const variants = {
      pass: "bg-green-100 text-green-800",
      fail: "bg-red-100 text-red-800", 
      warning: "bg-yellow-100 text-yellow-800",
      pending: "bg-gray-100 text-gray-800"
    }
    
    return (
      <Badge className={variants[status]}>
        {status === "pass" ? "Réussi" : status === "fail" ? "Échec" : status === "warning" ? "Attention" : "En attente"}
      </Badge>
    )
  }

  const passedTests = testResults.filter(t => t.status === "pass").length
  const totalTests = testResults.length

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TestTube className="h-5 w-5" />
          Suite de tests PWA
        </CardTitle>
        <CardDescription>
          Tests automatisés pour vérifier le bon fonctionnement de l'installation PWA
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Contrôles */}
        <div className="flex items-center justify-between">
          <div className="flex gap-2">
            <Button onClick={runTests} disabled={isRunning} size="sm">
              {isRunning ? <RefreshCw className="h-4 w-4 animate-spin mr-2" /> : <Play className="h-4 w-4 mr-2" />}
              {isRunning ? "Tests en cours..." : "Lancer les tests"}
            </Button>
            <Button onClick={promptInstall} disabled={!isInstallable} variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Tester l'installation
            </Button>
          </div>
          {lastRun && (
            <span className="text-xs text-muted-foreground">
              Dernière exécution: {lastRun.toLocaleTimeString()}
            </span>
          )}
        </div>

        {/* Résumé */}
        {testResults.length > 0 && (
          <Alert>
            <AlertDescription>
              <strong>{passedTests}/{totalTests} tests réussis</strong>
              {passedTests === totalTests ? " - Tous les tests sont passés !" : " - Certains tests nécessitent votre attention."}
            </AlertDescription>
          </Alert>
        )}

        <Separator />

        {/* Résultats des tests */}
        <div className="space-y-3">
          {testResults.map((test, index) => (
            <div key={index} className="flex items-start justify-between p-3 border rounded-lg">
              <div className="flex items-start gap-3">
                {getStatusIcon(test.status)}
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{test.name}</span>
                    {getStatusBadge(test.status)}
                  </div>
                  <p className="text-sm text-muted-foreground">{test.message}</p>
                  {test.details && (
                    <p className="text-xs text-muted-foreground bg-muted p-2 rounded">
                      {test.details}
                    </p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {testResults.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <TestTube className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Cliquez sur "Lancer les tests" pour commencer l'analyse PWA</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
