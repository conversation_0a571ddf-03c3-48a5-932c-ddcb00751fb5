"use client"

import { X } from "lucide-react"
import { useState } from "react"

interface IOSInstallGuideProps {
  onClose: () => void
}

export function IOSInstallGuide({ onClose }: IOSInstallGuideProps) {
  const [step, setStep] = useState(1)
  const totalSteps = 3

  const nextStep = () => {
    if (step < totalSteps) {
      setStep(step + 1)
    } else {
      onClose()
    }
  }

  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1)
    }
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="relative w-full max-w-md rounded-lg bg-white p-6 shadow-lg dark:bg-gray-800">
        <button
          onClick={onClose}
          className="absolute right-4 top-4 rounded-full p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-200"
        >
          <X size={20} />
        </button>

        <div className="mb-4 text-center">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white">Installer ACR Direct sur iOS</h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Étape {step} sur {totalSteps}
          </p>
          <div className="mt-2 flex justify-center">
            <div className="flex space-x-1">
              {[1, 2, 3].map((i) => (
                <div
                  key={i}
                  className={`h-2 w-2 rounded-full ${
                    i <= step ? 'bg-blue-500' : 'bg-gray-300 dark:bg-gray-600'
                  }`}
                />
              ))}
            </div>
          </div>
        </div>

        <div className="mb-6">
          {step === 1 && (
            <div className="text-center space-y-4">
              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <p className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
                  📱 Ouvrez cette page dans Safari
                </p>
                <p className="text-xs text-blue-700 dark:text-blue-300">
                  Assurez-vous d'utiliser Safari (pas Chrome ou autre navigateur)
                </p>
              </div>
              <p className="font-medium text-gray-900 dark:text-white">
                Appuyez sur l'icône de partage
                <span className="inline-block mx-1 p-1 bg-blue-100 dark:bg-blue-900 rounded">📤</span>
                en bas de Safari
              </p>
              <div className="relative mx-auto h-64 w-36 overflow-hidden rounded-3xl border-2 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 shadow-lg">
                <div className="h-8 bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                  <div className="text-xs text-gray-600 dark:text-gray-300">Safari</div>
                </div>
                <div className="flex-1 bg-gradient-to-b from-blue-50 to-white dark:from-blue-900/20 dark:to-gray-800"></div>
                <div className="absolute bottom-4 left-0 right-0 flex justify-center">
                  <div className="h-8 w-8 rounded-lg bg-blue-500 flex items-center justify-center shadow-md animate-pulse">
                    <span className="text-white text-sm">📤</span>
                  </div>
                </div>
              </div>
            </div>
          )}
          {step === 2 && (
            <div className="text-center">
              <p className="mb-4">Faites défiler et appuyez sur "Sur l'écran d'accueil"</p>
              <div className="relative mx-auto h-64 w-36 overflow-hidden rounded-3xl border">
                <div className="h-full w-full bg-gray-100 dark:bg-gray-700"></div>
                <div className="absolute bottom-0 left-0 right-0 h-32 rounded-t-xl bg-white p-2 dark:bg-gray-800">
                  <div className="mb-2 h-6 rounded bg-gray-200 dark:bg-gray-700"></div>
                  <div className="h-6 rounded bg-blue-100 dark:bg-blue-900"></div>
                </div>
              </div>
            </div>
          )}
          {step === 3 && (
            <div className="text-center">
              <p className="mb-4">Appuyez sur "Ajouter" en haut à droite</p>
              <div className="relative mx-auto h-64 w-36 overflow-hidden rounded-3xl border">
                <div className="h-8 bg-gray-200 dark:bg-gray-700"></div>
                <div className="flex h-56 flex-col items-center justify-center bg-white p-4 dark:bg-gray-800">
                  <div className="mb-4 h-16 w-16 rounded-xl bg-blue-500"></div>
                  <p className="mb-2 text-xs">ACR Direct</p>
                  <div className="mt-4 h-8 w-20 rounded-full bg-blue-500"></div>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="flex justify-between">
          {step > 1 ? (
            <button
              onClick={prevStep}
              className="rounded-md bg-gray-200 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
            >
              Précédent
            </button>
          ) : (
            <div></div>
          )}
          <button
            onClick={nextStep}
            className="rounded-md bg-blue-500 px-4 py-2 text-sm font-medium text-white hover:bg-blue-600"
          >
            {step === totalSteps ? "Terminer" : "Suivant"}
          </button>
        </div>
      </div>
    </div>
  )
}
