<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ACR Direct - Maskable Icon Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-preview {
            display: flex;
            gap: 30px;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        .icon-container {
            text-align: center;
        }
        .icon-canvas {
            border: 2px solid #ddd;
            margin: 10px 0;
            position: relative;
            background: white;
        }
        .safe-zone {
            position: absolute;
            border: 2px dashed #0d47a1;
            background: rgba(13, 71, 161, 0.1);
        }
        .logo-placeholder {
            position: absolute;
            background: #0d47a1;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }
        .mask-preview {
            margin-top: 20px;
            display: flex;
            gap: 15px;
            justify-content: center;
        }
        .mask-shape {
            width: 80px;
            height: 80px;
            background: #0d47a1;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
            font-weight: bold;
        }
        .mask-circle { border-radius: 50%; }
        .mask-rounded { border-radius: 20%; }
        .mask-square { border-radius: 0; }
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .specs {
            background: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #0d47a1;
            margin: 15px 0;
        }
        .download-section {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .button {
            background: #0d47a1;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .button:hover {
            background: #1565c0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 ACR Direct - Maskable Icon Generator</h1>
        <p>This tool helps you create proper maskable icons for the ACR Direct PWA to fix the cropping/zooming issue on home screens.</p>

        <div class="warning">
            <strong>⚠️ Current Issue:</strong> The PWA icons appear cropped/zoomed when installed because the same icon files are used for both "any" and "maskable" purposes. Maskable icons need different design specifications.
        </div>

        <h2>📐 Icon Specifications</h2>
        
        <div class="icon-preview">
            <div class="icon-container">
                <h3>192x192 Maskable Icon</h3>
                <div class="icon-canvas" style="width: 192px; height: 192px;">
                    <div class="safe-zone" style="top: 19px; left: 19px; width: 154px; height: 154px;"></div>
                    <div class="logo-placeholder" style="top: 46px; left: 46px; width: 100px; height: 100px;">
                        ACR<br>LOGO
                    </div>
                </div>
                <div class="specs">
                    <strong>Canvas:</strong> 192x192px<br>
                    <strong>Safe Zone:</strong> 154x154px<br>
                    <strong>Logo Max:</strong> 100x100px<br>
                    <strong>Margin:</strong> 19px all sides
                </div>
                <div class="mask-preview">
                    <div class="mask-shape mask-circle">ACR</div>
                    <div class="mask-shape mask-rounded">ACR</div>
                    <div class="mask-shape mask-square">ACR</div>
                </div>
            </div>

            <div class="icon-container">
                <h3>512x512 Maskable Icon</h3>
                <div class="icon-canvas" style="width: 256px; height: 256px; transform: scale(0.5); transform-origin: top left;">
                    <div class="safe-zone" style="top: 51px; left: 51px; width: 410px; height: 410px;"></div>
                    <div class="logo-placeholder" style="top: 121px; left: 121px; width: 270px; height: 270px; font-size: 24px;">
                        ACR<br>LOGO
                    </div>
                </div>
                <div class="specs">
                    <strong>Canvas:</strong> 512x512px<br>
                    <strong>Safe Zone:</strong> 410x410px<br>
                    <strong>Logo Max:</strong> 270x270px<br>
                    <strong>Margin:</strong> 51px all sides
                </div>
                <div class="mask-preview">
                    <div class="mask-shape mask-circle">ACR</div>
                    <div class="mask-shape mask-rounded">ACR</div>
                    <div class="mask-shape mask-square">ACR</div>
                </div>
            </div>
        </div>

        <div class="instructions">
            <h3>🎯 Design Instructions</h3>
            <ol>
                <li><strong>Background:</strong> Use white (#ffffff) background that fills the entire canvas</li>
                <li><strong>Logo Placement:</strong> Center the ACR Direct logo within the safe zone (blue dashed area)</li>
                <li><strong>Logo Size:</strong> Keep logo smaller than the safe zone to ensure visibility in all mask shapes</li>
                <li><strong>Safe Zone:</strong> The blue dashed area shows where content will always be visible</li>
                <li><strong>Margins:</strong> Ensure adequate spacing around the logo for clean appearance</li>
            </ol>
        </div>

        <div class="download-section">
            <h3>📥 Implementation Steps</h3>
            <ol>
                <li>Create the maskable icons using the specifications above</li>
                <li>Save them as:
                    <ul>
                        <li><code>icon-maskable-192x192.png</code></li>
                        <li><code>icon-maskable-512x512.png</code></li>
                    </ul>
                </li>
                <li>Place them in the <code>public/</code> directory</li>
                <li>The manifest.json has already been updated to reference these files</li>
                <li>Test the installation on various devices</li>
            </ol>
            
            <a href="https://maskable.app/" target="_blank" class="button">🧪 Test with Maskable.app</a>
            <a href="https://www.pwabuilder.com/" target="_blank" class="button">🔧 Validate with PWA Builder</a>
        </div>

        <h3>🔍 Current vs Fixed Comparison</h3>
        <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
            <tr style="background: #f8f9fa;">
                <th style="padding: 12px; border: 1px solid #ddd;">Aspect</th>
                <th style="padding: 12px; border: 1px solid #ddd;">Current (Problematic)</th>
                <th style="padding: 12px; border: 1px solid #ddd;">Fixed (Solution)</th>
            </tr>
            <tr>
                <td style="padding: 12px; border: 1px solid #ddd;"><strong>Icon Purpose</strong></td>
                <td style="padding: 12px; border: 1px solid #ddd;">Same file for "any" and "maskable"</td>
                <td style="padding: 12px; border: 1px solid #ddd;">Separate files for each purpose</td>
            </tr>
            <tr>
                <td style="padding: 12px; border: 1px solid #ddd;"><strong>Design</strong></td>
                <td style="padding: 12px; border: 1px solid #ddd;">Logo with padding (for "any" use)</td>
                <td style="padding: 12px; border: 1px solid #ddd;">Logo in safe zone with full background</td>
            </tr>
            <tr>
                <td style="padding: 12px; border: 1px solid #ddd;"><strong>Home Screen Result</strong></td>
                <td style="padding: 12px; border: 1px solid #ddd;">Logo appears cropped/zoomed</td>
                <td style="padding: 12px; border: 1px solid #ddd;">Logo displays with proper spacing</td>
            </tr>
            <tr>
                <td style="padding: 12px; border: 1px solid #ddd;"><strong>Mask Compatibility</strong></td>
                <td style="padding: 12px; border: 1px solid #ddd;">Poor (logo gets cut off)</td>
                <td style="padding: 12px; border: 1px solid #ddd;">Excellent (logo always visible)</td>
            </tr>
        </table>

        <div class="warning">
            <strong>💡 Pro Tip:</strong> After creating the maskable icons, test them at <a href="https://maskable.app/" target="_blank">maskable.app</a> to ensure they look good in all mask shapes before deploying.
        </div>
    </div>
</body>
</html>
