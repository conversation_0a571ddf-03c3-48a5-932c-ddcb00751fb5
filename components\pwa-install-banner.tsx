"use client"

import { useState, useEffect } from "react"
import { X } from "lucide-react"
import { usePWAInstall } from "@/lib/hooks/use-pwa-install"
import { IOSInstallGuide } from "./ios-install-guide"
import { WindowsInstallGuide } from "./windows-install-guide"

export function PWAInstallBanner() {
  const {
    isInstallable,
    isInstalled,
    promptInstall,
    isIOS,
    isWindows,
    showIOSGuide,
    closeIOSGuide,
    capabilities,
    installationDismissed,
    dismissInstallation
  } = usePWAInstall()
  const [showWindowsGuide, setShowWindowsGuide] = useState(false)
  const [browserType, setBrowserType] = useState<"chrome" | "edge" | "firefox" | "other">("chrome")
  const [showBanner, setShowBanner] = useState(false)
  const [dismissed, setDismissed] = useState(false)

  useEffect(() => {
    // Vérifier si la bannière a déjà été fermée dans cette session
    const sessionDismissed = sessionStorage.getItem("pwa-banner-dismissed") === "true"

    if (sessionDismissed || installationDismissed) {
      setDismissed(true)
      return
    }

    // Calculer le délai d'affichage basé sur les capacités
    let delay = 3000 // Délai par défaut

    // Délai plus court pour les navigateurs avec installation native
    if (capabilities.installMethod === "native") {
      delay = 2000
    }
    // Délai plus long pour iOS (installation manuelle)
    else if (capabilities.platform === "ios") {
      delay = 5000
    }

    // Afficher la bannière après le délai si l'app est installable et non installée
    const timer = setTimeout(() => {
      if (isInstallable && !isInstalled && capabilities.supportsPWA) {
        console.log("Affichage de la bannière d'installation", { capabilities })
        setShowBanner(true)
      } else {
        console.log("Conditions non remplies pour afficher la bannière:", {
          isInstallable,
          isInstalled,
          supportsPWA: capabilities.supportsPWA,
          installMethod: capabilities.installMethod
        })
      }
    }, delay)

    return () => clearTimeout(timer)
  }, [isInstallable, isInstalled, capabilities, installationDismissed])

  const handleDismiss = () => {
    setShowBanner(false)
    setDismissed(true)
    sessionStorage.setItem("pwa-banner-dismissed", "true")
  }

  const handlePermanentDismiss = () => {
    dismissInstallation()
    setShowBanner(false)
    setDismissed(true)
    sessionStorage.setItem("pwa-banner-dismissed", "true")
  }

  const handleInstall = () => {
    // Détecter le navigateur pour Windows
    if (capabilities.platform === "windows") {
      setBrowserType(capabilities.browserType)
      setShowWindowsGuide(true)
    }

    promptInstall()
    setShowBanner(false)
  }

  // Générer le message approprié selon les capacités
  const getInstallMessage = () => {
    if (capabilities.platform === "ios") {
      return "Ajoutez ACR Direct à votre écran d'accueil pour un accès rapide"
    } else if (capabilities.installMethod === "native") {
      return "Installez ACR Direct pour une expérience native optimisée"
    } else {
      return "Accédez rapidement à l'application depuis votre écran d'accueil"
    }
  }

  const getInstallButtonText = () => {
    if (capabilities.platform === "ios") {
      return "Voir le guide"
    } else if (capabilities.installMethod === "native") {
      return "Installer"
    } else {
      return "Voir comment faire"
    }
  }

  const closeWindowsGuide = () => {
    setShowWindowsGuide(false)
  }

  if (!showBanner || dismissed || isInstalled) return null

  return (
    <>
      <div className="fixed bottom-0 left-0 right-0 z-50 animate-slide-up bg-gradient-to-r from-blue-600 to-blue-700 shadow-xl">
        <div className="container mx-auto p-3 sm:p-4">
          {/* Version mobile compacte */}
          <div className="block sm:hidden">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 flex-shrink-0 rounded-lg bg-white p-1.5 shadow-md">
                <img
                  src="/android-chrome-192x192.png"
                  alt="ACR Direct"
                  className="h-full w-full object-contain rounded"
                />
              </div>
              <div className="text-white min-w-0 flex-1">
                <h3 className="font-semibold text-base truncate">Installer ACR Direct</h3>
                <p className="text-xs text-blue-100 opacity-90 truncate">
                  {capabilities.platform === "ios" ? "Ajoutez à l'écran d'accueil" : "Installation rapide"}
                </p>
              </div>
              <div className="flex items-center gap-1 flex-shrink-0">
                <button
                  onClick={handleInstall}
                  className="rounded-md bg-white px-2.5 py-1.5 text-xs font-semibold text-blue-600 shadow-md hover:bg-blue-50 transition-all duration-200"
                >
                  {capabilities.platform === "ios" ? "Guide" : "Installer"}
                </button>
                <button
                  onClick={handleDismiss}
                  className="rounded-full p-1.5 text-white/80 hover:bg-white/20 hover:text-white transition-all duration-200"
                  title="Fermer"
                >
                  <X size={14} />
                </button>
              </div>
            </div>
            <div className="mt-2 flex justify-center">
              <button
                onClick={handlePermanentDismiss}
                className="text-xs text-blue-200 hover:text-white underline underline-offset-2 transition-colors duration-200"
              >
                Ne plus proposer
              </button>
            </div>
          </div>

          {/* Version desktop */}
          <div className="hidden sm:block">
            <div className="flex items-center justify-between">
              <div className="flex items-center min-w-0 flex-1">
                <div className="mr-3 h-12 w-12 flex-shrink-0 rounded-xl bg-white p-2 shadow-md">
                  <img
                    src="/android-chrome-192x192.png"
                    alt="ACR Direct"
                    className="h-full w-full object-contain rounded-lg"
                  />
                </div>
                <div className="text-white min-w-0 flex-1">
                  <h3 className="font-bold text-lg truncate">Installer ACR Direct</h3>
                  <p className="text-sm text-blue-100 opacity-90 leading-tight max-h-10 overflow-hidden">
                    {getInstallMessage()}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2 flex-shrink-0">
                <button
                  onClick={handleInstall}
                  className="rounded-lg bg-white px-4 py-2 text-sm font-semibold text-blue-600 shadow-md hover:bg-blue-50 hover:shadow-lg transition-all duration-200 whitespace-nowrap"
                >
                  {getInstallButtonText()}
                </button>
                <button
                  onClick={handleDismiss}
                  className="rounded-full p-2 text-white/80 hover:bg-white/20 hover:text-white transition-all duration-200 flex-shrink-0"
                  title="Fermer"
                >
                  <X size={18} />
                </button>
              </div>
            </div>

            {/* Option "Ne plus afficher" */}
            <div className="mt-3 flex justify-center">
              <button
                onClick={handlePermanentDismiss}
                className="text-xs text-blue-200 hover:text-white underline underline-offset-2 transition-colors duration-200"
              >
                Ne plus me proposer l'installation
              </button>
            </div>
          </div>
        </div>
      </div>

      {showIOSGuide && <IOSInstallGuide onClose={closeIOSGuide} />}
      {showWindowsGuide && <WindowsInstallGuide onClose={closeWindowsGuide} browserType={browserType} />}
    </>
  )
}
