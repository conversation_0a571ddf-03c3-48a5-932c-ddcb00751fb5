"use client"

import type React from "react"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ImageIcon, Images, FileSlidersIcon as Slideshow, FileText, LayoutGrid, Sparkles, Zap, Eye } from "lucide-react"

export type ContentModelType = "richtext" | "single-image" | "gallery" | "carousel" | "grid"

interface ContentModel {
  id: ContentModelType
  name: string
  description: string
  icon: React.ReactNode
  features: string[]
  recommended?: boolean
  performance: "high" | "medium" | "low"
}

const contentModels: ContentModel[] = [
  {
    id: "richtext",
    name: "Texte enrichi",
    description: "Contenu texte avec mise en forme, images et liens",
    icon: <FileText className="h-8 w-8 text-primary" />,
    features: ["Éditeur WYSIWYG", "Images intégrées", "Liens", "Formatage"],
    performance: "high",
  },
  {
    id: "single-image",
    name: "Image unique",
    description: "Image responsive plein écran avec zoom et partage",
    icon: <ImageIcon className="h-8 w-8 text-primary" />,
    features: ["Plein écran", "Zoom", "Partage", "Légendes", "Responsive"],
    performance: "high",
    recommended: true,
  },
  {
    id: "gallery",
    name: "Galerie d'images",
    description: "Galerie moderne avec lightbox et navigation tactile",
    icon: <Images className="h-8 w-8 text-primary" />,
    features: ["Lightbox", "Navigation tactile", "Lazy loading", "Masonry"],
    performance: "medium",
  },
  {
    id: "carousel",
    name: "Carousel",
    description: "Diaporama interactif avec contrôles avancés",
    icon: <Slideshow className="h-8 w-8 text-primary" />,
    features: ["Auto-play", "Navigation", "Indicateurs", "Responsive"],
    performance: "medium",
  },
  {
    id: "grid",
    name: "Grille de contenu",
    description: "Mur infini d'éléments avec défilement optimisé",
    icon: <LayoutGrid className="h-8 w-8 text-primary" />,
    features: ["Défilement infini", "Filtres", "Recherche", "Responsive"],
    performance: "low",
    recommended: true,
  },
]

interface ContentTypeSelectorProps {
  value: ContentModelType
  onChange: (value: ContentModelType) => void
}

const getPerformanceIcon = (performance: string) => {
  switch (performance) {
    case "high":
      return <Zap className="h-3 w-3" />
    case "medium":
      return <Eye className="h-3 w-3" />
    case "low":
      return <Sparkles className="h-3 w-3" />
    default:
      return null
  }
}

const getPerformanceColor = (performance: string) => {
  switch (performance) {
    case "high":
      return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
    case "medium":
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
    case "low":
      return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
  }
}

export function ContentTypeSelector({ value, onChange }: ContentTypeSelectorProps) {
  return (
    <div className="space-y-6">
      <div>
        <Label className="text-base font-medium">Type de contenu</Label>
        <p className="text-sm text-muted-foreground mt-1">
          Choisissez le format qui convient le mieux à votre contenu
        </p>
      </div>

      <RadioGroup
        value={value}
        onValueChange={(val) => onChange(val as ContentModelType)}
        className="grid grid-cols-1 md:grid-cols-2 gap-4"
      >
        {contentModels.map((model) => (
          <Label key={model.id} htmlFor={`content-type-${model.id}`} className="cursor-pointer">
            <Card
              className={`p-4 h-full transition-all duration-200 hover:shadow-md ${
                value === model.id
                  ? "border-primary bg-primary/5 shadow-sm"
                  : "hover:border-primary/50 hover:bg-muted/30"
              }`}
            >
              <RadioGroupItem id={`content-type-${model.id}`} value={model.id} className="sr-only" />
              <div className="flex flex-col h-full space-y-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    {model.icon}
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{model.name}</span>
                        {model.recommended && (
                          <Badge variant="secondary" className="text-xs px-2 py-0">
                            <Sparkles className="h-3 w-3 mr-1" />
                            Recommandé
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                  <Badge
                    variant="outline"
                    className={`text-xs ${getPerformanceColor(model.performance)}`}
                  >
                    {getPerformanceIcon(model.performance)}
                    <span className="ml-1 capitalize">{model.performance}</span>
                  </Badge>
                </div>

                <p className="text-sm text-muted-foreground flex-1">{model.description}</p>

                <div className="flex flex-wrap gap-1 pt-2 border-t border-border/50">
                  {model.features.slice(0, 3).map((feature, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {feature}
                    </Badge>
                  ))}
                  {model.features.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{model.features.length - 3}
                    </Badge>
                  )}
                </div>
              </div>
            </Card>
          </Label>
        ))}
      </RadioGroup>
    </div>
  )
}
