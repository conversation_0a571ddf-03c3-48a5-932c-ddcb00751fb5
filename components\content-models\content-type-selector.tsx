"use client"

import type React from "react"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Card } from "@/components/ui/card"
import { ImageIcon, Images, FileSlidersIcon as Slideshow, FileText, LayoutGrid } from "lucide-react"

export type ContentModelType = "richtext" | "single-image" | "gallery" | "carousel" | "grid"

interface ContentModel {
  id: ContentModelType
  name: string
  description: string
  icon: React.ReactNode
  features: string[]
}

const contentModels: ContentModel[] = [
  {
    id: "richtext",
    name: "Texte enrichi",
    description: "Contenu texte avec mise en forme, images et liens",
    icon: <FileText className="h-8 w-8 text-primary" />,
    features: ["Éditeur WYSIWYG", "Images intégrées", "Liens", "Formatage"],
  },
  {
    id: "single-image",
    name: "Image unique",
    description: "Image responsive plein écran avec zoom et partage",
    icon: <ImageIcon className="h-8 w-8 text-primary" />,
    features: ["Plein écran", "Zoom", "Partage", "Légendes", "Responsive"],
  },
  {
    id: "gallery",
    name: "Galerie d'images",
    description: "Galerie moderne avec lightbox et navigation tactile",
    icon: <Images className="h-8 w-8 text-primary" />,
    features: ["Lightbox", "Navigation tactile", "Lazy loading", "Masonry"],
  },
  {
    id: "carousel",
    name: "Carousel",
    description: "Diaporama interactif avec contrôles avancés",
    icon: <Slideshow className="h-8 w-8 text-primary" />,
    features: ["Auto-play", "Navigation", "Indicateurs", "Responsive"],
  },
  {
    id: "grid",
    name: "Grille de contenu",
    description: "Mur infini d'éléments avec défilement optimisé",
    icon: <LayoutGrid className="h-8 w-8 text-primary" />,
    features: ["Défilement infini", "Filtres", "Recherche", "Responsive"],
  },
]

interface ContentTypeSelectorProps {
  value: ContentModelType
  onChange: (value: ContentModelType) => void
}

export function ContentTypeSelector({ value, onChange }: ContentTypeSelectorProps) {
  return (
    <div className="space-y-6">
      <div>
        <Label className="text-base font-medium">Type de contenu</Label>
        <p className="text-sm text-muted-foreground mt-1">
          Choisissez le format qui convient le mieux à votre contenu
        </p>
      </div>

      <RadioGroup
        value={value}
        onValueChange={(val) => onChange(val as ContentModelType)}
        className="grid grid-cols-1 md:grid-cols-2 gap-4"
      >
        {contentModels.map((model) => (
          <Label key={model.id} htmlFor={`content-type-${model.id}`} className="cursor-pointer">
            <Card
              className={`p-4 h-full transition-all duration-200 hover:shadow-md ${
                value === model.id
                  ? "border-primary bg-primary/5 shadow-sm"
                  : "hover:border-primary/50 hover:bg-muted/30"
              }`}
            >
              <RadioGroupItem id={`content-type-${model.id}`} value={model.id} className="sr-only" />
              <div className="flex flex-col h-full space-y-3">
                <div className="flex items-center gap-3">
                  {model.icon}
                  <span className="font-medium">{model.name}</span>
                </div>

                <p className="text-sm text-muted-foreground flex-1">{model.description}</p>

                <div className="flex flex-wrap gap-1 pt-2 border-t border-border/50">
                  {model.features.map((feature, index) => (
                    <span key={index} className="text-xs text-muted-foreground bg-muted/50 px-2 py-1 rounded">
                      {feature}
                    </span>
                  ))}
                </div>
              </div>
            </Card>
          </Label>
        ))}
      </RadioGroup>
    </div>
  )
}
