"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { 
  Activity, 
  Zap, 
  Database, 
  Wifi, 
  Clock, 
  TrendingUp,
  RefreshCw,
  AlertTriangle
} from "lucide-react"

interface PerformanceMetrics {
  loadTime: number
  cacheHitRatio: number
  serviceWorkerStatus: string
  offlineCapability: boolean
  installPromptDelay: number
  networkStatus: "online" | "offline"
  cacheSize: number
  lastUpdate: Date
}

export function PWAPerformanceMonitor() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    loadTime: 0,
    cacheHitRatio: 0,
    serviceWorkerStatus: "checking",
    offlineCapability: false,
    installPromptDelay: 0,
    networkStatus: "online",
    cacheSize: 0,
    lastUpdate: new Date()
  })
  const [isMonitoring, setIsMonitoring] = useState(false)
  const [recommendations, setRecommendations] = useState<string[]>([])

  const measurePerformance = async () => {
    setIsMonitoring(true)
    
    try {
      // Mesurer le temps de chargement
      const navigationTiming = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      const loadTime = navigationTiming ? navigationTiming.loadEventEnd - navigationTiming.fetchStart : 0

      // Vérifier le Service Worker
      let swStatus = "inactive"
      let offlineCapable = false
      if ('serviceWorker' in navigator) {
        const registration = await navigator.serviceWorker.getRegistration()
        if (registration) {
          swStatus = registration.active ? "active" : "installing"
          offlineCapable = true
        }
      }

      // Estimer la taille du cache
      let cacheSize = 0
      if ('caches' in window) {
        try {
          const cacheNames = await caches.keys()
          for (const cacheName of cacheNames) {
            const cache = await caches.open(cacheName)
            const keys = await cache.keys()
            cacheSize += keys.length
          }
        } catch (error) {
          console.warn("Impossible de calculer la taille du cache:", error)
        }
      }

      // Calculer le ratio de cache hit (simulation basée sur les performances)
      const cacheHitRatio = loadTime < 1000 ? 85 : loadTime < 2000 ? 70 : 50

      // Statut réseau
      const networkStatus = navigator.onLine ? "online" : "offline"

      // Délai d'affichage du prompt d'installation (simulation)
      const installPromptDelay = 3000

      const newMetrics: PerformanceMetrics = {
        loadTime: Math.round(loadTime),
        cacheHitRatio,
        serviceWorkerStatus: swStatus,
        offlineCapability: offlineCapable,
        installPromptDelay,
        networkStatus,
        cacheSize,
        lastUpdate: new Date()
      }

      setMetrics(newMetrics)
      generateRecommendations(newMetrics)
    } catch (error) {
      console.error("Erreur lors de la mesure des performances:", error)
    } finally {
      setIsMonitoring(false)
    }
  }

  const generateRecommendations = (metrics: PerformanceMetrics) => {
    const recs: string[] = []

    if (metrics.loadTime > 3000) {
      recs.push("Temps de chargement élevé - Optimisez le cache et les ressources")
    }

    if (metrics.cacheHitRatio < 70) {
      recs.push("Ratio de cache faible - Améliorez la stratégie de mise en cache")
    }

    if (metrics.serviceWorkerStatus !== "active") {
      recs.push("Service Worker inactif - Vérifiez l'enregistrement du SW")
    }

    if (!metrics.offlineCapability) {
      recs.push("Capacité hors ligne limitée - Implémentez une stratégie de cache robuste")
    }

    if (metrics.installPromptDelay > 5000) {
      recs.push("Délai d'installation trop long - Réduisez le délai d'affichage")
    }

    if (metrics.cacheSize < 10) {
      recs.push("Cache insuffisant - Augmentez le nombre de ressources mises en cache")
    }

    if (recs.length === 0) {
      recs.push("Excellentes performances PWA ! Continuez ainsi.")
    }

    setRecommendations(recs)
  }

  useEffect(() => {
    measurePerformance()
    
    // Mesurer les performances périodiquement
    const interval = setInterval(measurePerformance, 30000) // Toutes les 30 secondes
    
    return () => clearInterval(interval)
  }, [])

  const getPerformanceScore = () => {
    let score = 100
    
    if (metrics.loadTime > 3000) score -= 20
    else if (metrics.loadTime > 2000) score -= 10
    
    if (metrics.cacheHitRatio < 70) score -= 15
    else if (metrics.cacheHitRatio < 85) score -= 5
    
    if (metrics.serviceWorkerStatus !== "active") score -= 25
    if (!metrics.offlineCapability) score -= 20
    if (metrics.installPromptDelay > 5000) score -= 10
    
    return Math.max(0, score)
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600"
    if (score >= 70) return "text-yellow-600"
    return "text-red-600"
  }

  const getScoreBadge = (score: number) => {
    if (score >= 90) return "bg-green-100 text-green-800"
    if (score >= 70) return "bg-yellow-100 text-yellow-800"
    return "bg-red-100 text-red-800"
  }

  const performanceScore = getPerformanceScore()

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5" />
          Moniteur de performances PWA
        </CardTitle>
        <CardDescription>
          Surveillance en temps réel des performances et de l'efficacité PWA
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Score global */}
        <div className="text-center space-y-2">
          <div className={`text-4xl font-bold ${getScoreColor(performanceScore)}`}>
            {performanceScore}/100
          </div>
          <Badge className={getScoreBadge(performanceScore)}>
            {performanceScore >= 90 ? "Excellent" : performanceScore >= 70 ? "Bon" : "À améliorer"}
          </Badge>
          <Progress value={performanceScore} className="w-full" />
        </div>

        <Separator />

        {/* Métriques détaillées */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <h4 className="font-semibold flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Performance
            </h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Temps de chargement:</span>
                <Badge variant="outline" className={metrics.loadTime > 3000 ? "text-red-600" : metrics.loadTime > 2000 ? "text-yellow-600" : "text-green-600"}>
                  {metrics.loadTime}ms
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Ratio de cache:</span>
                <Badge variant="outline" className={metrics.cacheHitRatio < 70 ? "text-red-600" : metrics.cacheHitRatio < 85 ? "text-yellow-600" : "text-green-600"}>
                  {metrics.cacheHitRatio}%
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Statut réseau:</span>
                <Badge variant="outline" className={metrics.networkStatus === "online" ? "text-green-600" : "text-red-600"}>
                  <Wifi className="h-3 w-3 mr-1" />
                  {metrics.networkStatus === "online" ? "En ligne" : "Hors ligne"}
                </Badge>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <h4 className="font-semibold flex items-center gap-2">
              <Database className="h-4 w-4" />
              Cache & SW
            </h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Service Worker:</span>
                <Badge variant="outline" className={metrics.serviceWorkerStatus === "active" ? "text-green-600" : "text-red-600"}>
                  {metrics.serviceWorkerStatus === "active" ? "Actif" : "Inactif"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Capacité hors ligne:</span>
                <Badge variant="outline" className={metrics.offlineCapability ? "text-green-600" : "text-red-600"}>
                  {metrics.offlineCapability ? "Oui" : "Non"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Éléments en cache:</span>
                <Badge variant="outline">
                  {metrics.cacheSize}
                </Badge>
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* Recommandations */}
        <div className="space-y-3">
          <h4 className="font-semibold flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Recommandations
          </h4>
          <div className="space-y-2">
            {recommendations.map((rec, index) => (
              <div key={index} className="flex items-start gap-2 p-2 bg-muted rounded-lg">
                <AlertTriangle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                <span className="text-sm">{rec}</span>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* Contrôles */}
        <div className="flex items-center justify-between">
          <Button onClick={measurePerformance} disabled={isMonitoring} size="sm" variant="outline">
            {isMonitoring ? <RefreshCw className="h-4 w-4 animate-spin mr-2" /> : <RefreshCw className="h-4 w-4 mr-2" />}
            {isMonitoring ? "Analyse..." : "Actualiser"}
          </Button>
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Clock className="h-3 w-3" />
            Dernière mise à jour: {metrics.lastUpdate.toLocaleTimeString()}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
