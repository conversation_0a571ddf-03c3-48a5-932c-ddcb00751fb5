"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ExternalLink, Search, Filter, Grid3X3, List, Loader2, Eye } from "lucide-react"
import { useIsMobile } from "@/hooks/use-mobile"
import { OptimizedImage } from "@/components/optimized-image"

interface GridItem {
  id: string
  title: string
  description?: string
  imageUrl?: string
  link?: string
  category?: string
  tags?: string[]
}

interface GridViewerProps {
  items: GridItem[]
  enableInfiniteScroll?: boolean
  itemsPerPage?: number
  enableSearch?: boolean
  enableFilters?: boolean
  layout?: "grid" | "list"
  showItemCount?: boolean
}

export function GridViewer({
  items,
  enableInfiniteScroll = true,
  itemsPerPage = 12,
  enableSearch = true,
  enableFilters = true,
  layout = "grid",
  showItemCount = true
}: GridViewerProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [currentLayout, setCurrentLayout] = useState(layout)
  const [displayedItems, setDisplayedItems] = useState<GridItem[]>([])
  const [visibleCount, setVisibleCount] = useState(itemsPerPage)
  const [isLoading, setIsLoading] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const observerRef = useRef<HTMLDivElement>(null)
  const isMobile = useIsMobile()

  // Filter and search items
  const filteredItems = items.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.tags?.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))

    const matchesCategory = selectedCategory === "all" || item.category === selectedCategory

    return matchesSearch && matchesCategory
  })

  // Get unique categories
  const categories = Array.from(new Set(items.map(item => item.category).filter(Boolean)))

  // Load more items
  const loadMoreItems = useCallback(() => {
    if (isLoading || !hasMore) return

    setIsLoading(true)

    // Simulate loading delay for better UX
    setTimeout(() => {
      const newVisibleCount = Math.min(visibleCount + itemsPerPage, filteredItems.length)
      setVisibleCount(newVisibleCount)
      setDisplayedItems(filteredItems.slice(0, newVisibleCount))
      setHasMore(newVisibleCount < filteredItems.length)
      setIsLoading(false)
    }, 300)
  }, [visibleCount, itemsPerPage, filteredItems, isLoading, hasMore])

  // Intersection Observer for infinite scroll
  useEffect(() => {
    if (!enableInfiniteScroll) return

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !isLoading) {
          loadMoreItems()
        }
      },
      { threshold: 0.1 }
    )

    if (observerRef.current) {
      observer.observe(observerRef.current)
    }

    return () => observer.disconnect()
  }, [loadMoreItems, hasMore, isLoading, enableInfiniteScroll])

  // Update displayed items when filters change
  useEffect(() => {
    const newVisibleCount = enableInfiniteScroll ? Math.min(itemsPerPage, filteredItems.length) : filteredItems.length
    setVisibleCount(newVisibleCount)
    setDisplayedItems(filteredItems.slice(0, newVisibleCount))
    setHasMore(newVisibleCount < filteredItems.length)
  }, [filteredItems, itemsPerPage, enableInfiniteScroll])

  if (items.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-8 border-2 border-dashed border-muted-foreground/30 rounded-lg">
        <div className="text-muted-foreground text-center">
          <Grid3X3 className="h-12 w-12 mx-auto mb-2" />
          <p className="text-sm">Aucun élément à afficher</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with Search and Filters */}
      {(enableSearch || enableFilters || showItemCount) && (
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex items-center gap-4">
              {showItemCount && (
                <Badge variant="outline" className="text-sm">
                  {filteredItems.length} élément{filteredItems.length > 1 ? 's' : ''}
                  {filteredItems.length !== items.length && ` sur ${items.length}`}
                </Badge>
              )}
            </div>

            <div className="flex items-center gap-2">
              {/* Layout Toggle */}
              <div className="flex border rounded-lg p-1">
                <Button
                  variant={currentLayout === "grid" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setCurrentLayout("grid")}
                  className="h-8 w-8 p-0"
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={currentLayout === "list" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setCurrentLayout("list")}
                  className="h-8 w-8 p-0"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            {enableSearch && (
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Rechercher..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            )}

            {/* Category Filter */}
            {enableFilters && categories.length > 0 && (
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-full sm:w-48">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Catégorie" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Toutes les catégories</SelectItem>
                  {categories.map(category => (
                    <SelectItem key={category} value={category!}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>
        </div>
      )}

      {/* Items Grid/List */}
      {filteredItems.length === 0 ? (
        <div className="text-center p-8 text-muted-foreground">
          <Search className="h-12 w-12 mx-auto mb-2" />
          <p className="text-sm">Aucun résultat trouvé</p>
          <p className="text-xs mt-1">Essayez de modifier vos critères de recherche</p>
        </div>
      ) : (
        <>
          <div className={
            currentLayout === "grid"
              ? `grid grid-cols-1 ${isMobile ? 'sm:grid-cols-2' : 'sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'} gap-6`
              : "space-y-4"
          }>
            {displayedItems.map((item, index) => (
              <Card
                key={item.id}
                className={`overflow-hidden transition-all duration-200 hover:shadow-lg ${
                  currentLayout === "list" ? "flex flex-row" : ""
                }`}
              >
                {item.imageUrl && (
                  <div className={
                    currentLayout === "list"
                      ? "w-48 flex-shrink-0"
                      : "aspect-[16/9] overflow-hidden"
                  }>
                    <OptimizedImage
                      src={item.imageUrl}
                      alt={item.title}
                      className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                      loading="lazy"
                      sizes={currentLayout === "list" ? "192px" : "(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"}
                    />
                  </div>
                )}

                <div className="flex flex-col flex-1">
                  <CardContent className="p-4 flex-1">
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="font-semibold text-lg leading-tight">{item.title}</h3>
                      {item.category && (
                        <Badge variant="secondary" className="ml-2 text-xs">
                          {item.category}
                        </Badge>
                      )}
                    </div>

                    {item.description && (
                      <p className="text-muted-foreground text-sm leading-relaxed line-clamp-3">
                        {item.description}
                      </p>
                    )}

                    {item.tags && item.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-3">
                        {item.tags.slice(0, 3).map((tag, tagIndex) => (
                          <Badge key={tagIndex} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                        {item.tags.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{item.tags.length - 3}
                          </Badge>
                        )}
                      </div>
                    )}
                  </CardContent>

                  {item.link && (
                    <CardFooter className="p-4 pt-0">
                      <Button asChild size="sm" className="w-full">
                        <a href={item.link} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="h-4 w-4 mr-2" />
                          Voir plus
                        </a>
                      </Button>
                    </CardFooter>
                  )}
                </div>
              </Card>
            ))}
          </div>

          {/* Infinite Scroll Trigger */}
          {enableInfiniteScroll && hasMore && (
            <div ref={observerRef} className="flex justify-center py-8">
              {isLoading && <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />}
            </div>
          )}

          {/* Load More Button (fallback for infinite scroll) */}
          {!enableInfiniteScroll && hasMore && (
            <div className="flex justify-center">
              <Button
                onClick={loadMoreItems}
                disabled={isLoading}
                variant="outline"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Chargement...
                  </>
                ) : (
                  <>
                    <Eye className="h-4 w-4 mr-2" />
                    Voir plus ({filteredItems.length - visibleCount} restants)
                  </>
                )}
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  )
}
