"use client"

import type React from "react"

import { useState, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { ImagePlus, X, Loader2 } from "lucide-react"

interface SingleImageModelProps {
  imageUrl: string | null
  caption: string
  altText: string
  onImageChange: (file: File | null) => void
  onCaptionChange: (caption: string) => void
  onAltTextChange: (altText: string) => void
  isUploading?: boolean
}

export function SingleImageModel({
  imageUrl,
  caption,
  altText,
  onImageChange,
  onCaptionChange,
  onAltTextChange,
  isUploading = false,
}: SingleImageModelProps) {
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(imageUrl)

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      onImageChange(file)

      // Create preview
      const reader = new FileReader()
      reader.onload = (event) => {
        setPreviewUrl(event.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleRemoveImage = () => {
    setPreviewUrl(null)
    onImageChange(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label className="text-base font-medium">Image</Label>
          {previewUrl && (
            <div className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => fileInputRef.current?.click()}
                disabled={isUploading}
              >
                <ImagePlus className="h-4 w-4 mr-2" />
                Remplacer
              </Button>
              <Button
                type="button"
                variant="destructive"
                size="sm"
                onClick={handleRemoveImage}
                disabled={isUploading}
              >
                <X className="h-4 w-4 mr-2" />
                Supprimer
              </Button>
            </div>
          )}
        </div>

        <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg transition-colors hover:border-muted-foreground/50">
          {previewUrl ? (
            <div className="relative">
              <div className="relative aspect-video w-full overflow-hidden rounded-lg bg-muted/30">
                <img
                  src={previewUrl}
                  alt="Aperçu"
                  className="object-contain w-full h-full"
                />
                {isUploading && (
                  <div className="absolute inset-0 bg-black/50 flex items-center justify-center rounded-lg">
                    <div className="text-white text-center">
                      <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
                      <p className="text-sm">Téléchargement...</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div
              className="flex flex-col items-center justify-center w-full h-48 bg-muted/30 rounded-lg cursor-pointer transition-colors hover:bg-muted/50"
              onClick={() => fileInputRef.current?.click()}
            >
              {isUploading ? (
                <Loader2 className="h-10 w-10 text-muted-foreground animate-spin" />
              ) : (
                <>
                  <ImagePlus className="h-10 w-10 text-muted-foreground mb-2" />
                  <p className="text-sm text-muted-foreground">Cliquez pour ajouter une image</p>
                </>
              )}
            </div>
          )}
          <input ref={fileInputRef} type="file" accept="image/*" onChange={handleFileChange} className="hidden" />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="caption" className="text-sm font-medium">
            Légende
            <span className="text-muted-foreground font-normal ml-1">(optionnelle)</span>
          </Label>
          <Textarea
            id="caption"
            value={caption}
            onChange={(e) => onCaptionChange(e.target.value)}
            placeholder="Ajoutez une légende descriptive pour votre image..."
            rows={3}
            className="resize-none"
          />
          <p className="text-xs text-muted-foreground">
            La légende sera affichée sous l'image pour donner du contexte
          </p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="altText" className="text-sm font-medium">
            Texte alternatif
            <span className="text-red-500 ml-1">*</span>
          </Label>
          <Textarea
            id="altText"
            value={altText}
            onChange={(e) => onAltTextChange(e.target.value)}
            placeholder="Description précise de l'image pour l'accessibilité..."
            rows={3}
            className="resize-none"
            required
          />
          <p className="text-xs text-muted-foreground">
            Description détaillée pour les lecteurs d'écran et l'accessibilité
          </p>
        </div>
      </div>

      {/* Image Preview Info */}
      {previewUrl && (
        <div className="p-4 bg-muted/30 rounded-lg">
          <h4 className="text-sm font-medium mb-2">Aperçu de l'affichage</h4>
          <div className="text-xs text-muted-foreground space-y-1">
            <p>• L'image sera affichée en plein écran avec zoom disponible</p>
            <p>• Les utilisateurs pourront télécharger et partager l'image</p>
            <p>• L'image sera optimisée automatiquement pour tous les appareils</p>
            {caption && <p>• La légende sera affichée sous l'image</p>}
          </div>
        </div>
      )}
    </div>
  )
}
