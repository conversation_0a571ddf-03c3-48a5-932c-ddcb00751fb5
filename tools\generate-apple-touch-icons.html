<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ACR Direct - Apple Touch Icon Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .icon-spec {
            text-align: center;
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            background: #fafafa;
        }
        .icon-canvas {
            margin: 10px auto;
            position: relative;
            background: white;
            border: 2px solid #ccc;
            border-radius: 8px;
        }
        .logo-area {
            position: absolute;
            border: 2px dashed #0d47a1;
            background: rgba(13, 71, 161, 0.1);
            border-radius: 4px;
        }
        .logo-placeholder {
            position: absolute;
            background: #0d47a1;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 10px;
        }
        .specs {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-size: 12px;
        }
        .comparison {
            display: flex;
            gap: 30px;
            margin: 30px 0;
            justify-content: center;
        }
        .comparison-item {
            text-align: center;
            flex: 1;
            max-width: 300px;
        }
        .comparison-icon {
            width: 100px;
            height: 100px;
            margin: 10px auto;
            border: 2px solid #ddd;
            border-radius: 20px;
            position: relative;
            background: white;
        }
        .current-design {
            background: #ffebee;
            border-color: #f44336;
        }
        .fixed-design {
            background: #e8f5e8;
            border-color: #4caf50;
        }
        .current-logo {
            width: 60px;
            height: 60px;
            background: #0d47a1;
            margin: 20px auto;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
            font-weight: bold;
        }
        .fixed-logo {
            width: 80px;
            height: 80px;
            background: #0d47a1;
            margin: 10px auto;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .button {
            background: #0d47a1;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .button:hover {
            background: #1565c0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            padding: 12px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🍎 ACR Direct - Apple Touch Icon Generator</h1>
        <p>Créez des icônes Apple Touch optimisées pour "Ajouter à l'écran d'accueil" sur iOS Safari.</p>

        <div class="warning">
            <strong>⚠️ Problème identifié:</strong> L'installation via "Ajouter à l'écran d'accueil" (Safari iOS) affiche l'icône avec un zoom/crop excessif, contrairement à l'installation native PWA qui fonctionne correctement.
        </div>

        <h2>🔍 Comparaison: Actuel vs Corrigé</h2>
        <div class="comparison">
            <div class="comparison-item">
                <h3>❌ Actuel (Problématique)</h3>
                <div class="comparison-icon current-design">
                    <div class="current-logo">ACR</div>
                </div>
                <p><strong>Design:</strong> Logo avec padding (comme Android)</p>
                <p><strong>Résultat iOS:</strong> Logo apparaît trop petit/zoomé</p>
            </div>
            <div class="comparison-item">
                <h3>✅ Corrigé (Solution)</h3>
                <div class="comparison-icon fixed-design">
                    <div class="fixed-logo">ACR</div>
                </div>
                <p><strong>Design:</strong> Logo plus grand, padding minimal</p>
                <p><strong>Résultat iOS:</strong> Logo bien proportionné</p>
            </div>
        </div>

        <h2>📐 Spécifications des Apple Touch Icons</h2>
        <div class="icon-grid">
            <div class="icon-spec">
                <h3>180x180 (Principal)</h3>
                <div class="icon-canvas" style="width: 90px; height: 90px;">
                    <div class="logo-area" style="top: 10px; left: 10px; width: 70px; height: 70px;"></div>
                    <div class="logo-placeholder" style="top: 15px; left: 15px; width: 60px; height: 60px;">ACR</div>
                </div>
                <div class="specs">
                    <strong>Canvas:</strong> 180x180px<br>
                    <strong>Logo:</strong> ~140x140px<br>
                    <strong>Margin:</strong> 20px<br>
                    <strong>Usage:</strong> iPhone 6 Plus+
                </div>
            </div>

            <div class="icon-spec">
                <h3>152x152</h3>
                <div class="icon-canvas" style="width: 76px; height: 76px;">
                    <div class="logo-area" style="top: 8px; left: 8px; width: 60px; height: 60px;"></div>
                    <div class="logo-placeholder" style="top: 12px; left: 12px; width: 52px; height: 52px; font-size: 8px;">ACR</div>
                </div>
                <div class="specs">
                    <strong>Canvas:</strong> 152x152px<br>
                    <strong>Logo:</strong> ~120x120px<br>
                    <strong>Margin:</strong> 16px<br>
                    <strong>Usage:</strong> iPad Retina
                </div>
            </div>

            <div class="icon-spec">
                <h3>120x120</h3>
                <div class="icon-canvas" style="width: 60px; height: 60px;">
                    <div class="logo-area" style="top: 6px; left: 6px; width: 48px; height: 48px;"></div>
                    <div class="logo-placeholder" style="top: 9px; left: 9px; width: 42px; height: 42px; font-size: 7px;">ACR</div>
                </div>
                <div class="specs">
                    <strong>Canvas:</strong> 120x120px<br>
                    <strong>Logo:</strong> ~96x96px<br>
                    <strong>Margin:</strong> 12px<br>
                    <strong>Usage:</strong> iPhone Retina
                </div>
            </div>
        </div>

        <h2>📋 Toutes les tailles requises</h2>
        <table>
            <tr>
                <th>Taille</th>
                <th>Fichier</th>
                <th>Usage</th>
                <th>Logo Area</th>
                <th>Margin</th>
            </tr>
            <tr>
                <td>180x180</td>
                <td>apple-touch-icon-180x180.png</td>
                <td>iPhone 6 Plus et plus récent</td>
                <td>140x140px</td>
                <td>20px</td>
            </tr>
            <tr>
                <td>152x152</td>
                <td>apple-touch-icon-152x152.png</td>
                <td>iPad Retina</td>
                <td>120x120px</td>
                <td>16px</td>
            </tr>
            <tr>
                <td>144x144</td>
                <td>apple-touch-icon-144x144.png</td>
                <td>iPad non-Retina</td>
                <td>114x114px</td>
                <td>15px</td>
            </tr>
            <tr>
                <td>120x120</td>
                <td>apple-touch-icon-120x120.png</td>
                <td>iPhone Retina</td>
                <td>96x96px</td>
                <td>12px</td>
            </tr>
            <tr>
                <td>114x114</td>
                <td>apple-touch-icon-114x114.png</td>
                <td>iPhone 4</td>
                <td>90x90px</td>
                <td>12px</td>
            </tr>
            <tr>
                <td>76x76</td>
                <td>apple-touch-icon-76x76.png</td>
                <td>iPad</td>
                <td>60x60px</td>
                <td>8px</td>
            </tr>
            <tr>
                <td>72x72</td>
                <td>apple-touch-icon-72x72.png</td>
                <td>iPad non-Retina</td>
                <td>56x56px</td>
                <td>8px</td>
            </tr>
            <tr>
                <td>60x60</td>
                <td>apple-touch-icon-60x60.png</td>
                <td>iPhone</td>
                <td>48x48px</td>
                <td>6px</td>
            </tr>
            <tr>
                <td>57x57</td>
                <td>apple-touch-icon-57x57.png</td>
                <td>iPhone original</td>
                <td>45x45px</td>
                <td>6px</td>
            </tr>
        </table>

        <div class="instructions">
            <h3>🎨 Instructions de création</h3>
            <ol>
                <li><strong>Utilisez le logo ACR Direct existant</strong></li>
                <li><strong>Créez un canvas blanc</strong> pour chaque taille</li>
                <li><strong>Centrez le logo</strong> dans la zone recommandée</li>
                <li><strong>Le logo doit être plus grand</strong> que dans les icônes Android actuelles</li>
                <li><strong>Gardez un padding minimal</strong> (voir tableau ci-dessus)</li>
                <li><strong>Exportez en PNG</strong> avec les noms de fichiers exacts</li>
                <li><strong>Placez dans le dossier public/</strong></li>
            </ol>
        </div>

        <div class="success">
            <h3>✅ Résultat attendu</h3>
            <p>Après création des Apple Touch Icons:</p>
            <ul>
                <li>✅ Installation native PWA (Chrome/Edge): Continue de fonctionner correctement</li>
                <li>✅ "Ajouter à l'écran d'accueil" (Safari iOS): Affichage correct sans zoom excessif</li>
                <li>✅ Logo ACR Direct clairement visible sur tous les appareils iOS</li>
                <li>✅ Cohérence visuelle entre toutes les méthodes d'installation</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="/admin/diagnostics" class="button">🔍 Tester les icônes</a>
            <a href="https://realfavicongenerator.net/" target="_blank" class="button">🛠️ Générateur en ligne</a>
        </div>

        <div class="warning">
            <strong>💡 Conseil:</strong> Après création des icônes, testez l'installation sur un vrai appareil iOS avec Safari pour vérifier l'affichage.
        </div>
    </div>
</body>
</html>
