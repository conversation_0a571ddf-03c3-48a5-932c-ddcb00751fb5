"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { <PERSON>alog, DialogContent } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ChevronLeft, ChevronRight, X, Download, Share2, ZoomIn, ZoomOut, Grid3X3, LayoutGrid, Loader2 } from "lucide-react"
import { useIsMobile } from "@/hooks/use-mobile"
import { OptimizedImage } from "@/components/optimized-image"

interface GalleryImage {
  url: string
  altText: string
  caption?: string
}

interface GalleryViewerProps {
  images: GalleryImage[]
  columns?: number
  layout?: "grid" | "masonry"
  enableLazyLoading?: boolean
}

export function GalleryViewer({
  images,
  columns = 4,
  layout = "grid",
  enableLazyLoading = true
}: GalleryViewerProps) {
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null)
  const [loadedImages, setLoadedImages] = useState<Set<number>>(new Set())
  const [zoom, setZoom] = useState(1)
  const [isDownloading, setIsDownloading] = useState(false)
  const [isSharing, setIsSharing] = useState(false)
  const galleryRef = useRef<HTMLDivElement>(null)
  const isMobile = useIsMobile()

  // Handle image loading
  const handleImageLoad = useCallback((index: number) => {
    setLoadedImages(prev => new Set([...prev, index]))
  }, [])

  // Navigation functions
  const openLightbox = (index: number) => {
    setSelectedImageIndex(index)
    setZoom(1) // Reset zoom when opening new image
  }

  const closeLightbox = () => {
    setSelectedImageIndex(null)
    setZoom(1)
  }

  const goToPrevious = () => {
    if (selectedImageIndex === null) return
    setSelectedImageIndex((selectedImageIndex - 1 + images.length) % images.length)
    setZoom(1)
  }

  const goToNext = () => {
    if (selectedImageIndex === null) return
    setSelectedImageIndex((selectedImageIndex + 1) % images.length)
    setZoom(1)
  }

  // Zoom controls
  const handleZoomIn = () => setZoom(prev => Math.min(prev + 0.25, 3))
  const handleZoomOut = () => setZoom(prev => Math.max(prev - 0.25, 0.5))

  // Download and share functions
  const handleDownload = async () => {
    if (selectedImageIndex === null) return

    setIsDownloading(true)
    try {
      const image = images[selectedImageIndex]
      const response = await fetch(image.url)
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `image-${selectedImageIndex + 1}.jpg`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Erreur lors du téléchargement:', error)
    } finally {
      setIsDownloading(false)
    }
  }

  const handleShare = async () => {
    if (selectedImageIndex === null) return

    setIsSharing(true)
    try {
      const image = images[selectedImageIndex]
      if (navigator.share) {
        await navigator.share({
          title: image.altText || `Image ${selectedImageIndex + 1}`,
          text: image.caption || image.altText || 'Image partagée',
          url: image.url,
        })
      } else {
        await navigator.clipboard.writeText(image.url)
        alert('URL copiée dans le presse-papier')
      }
    } catch (error) {
      console.error('Erreur lors du partage:', error)
    } finally {
      setIsSharing(false)
    }
  }

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (selectedImageIndex === null) return

      switch (event.key) {
        case 'ArrowLeft':
          goToPrevious()
          break
        case 'ArrowRight':
          goToNext()
          break
        case 'Escape':
          closeLightbox()
          break
        case '+':
        case '=':
          handleZoomIn()
          break
        case '-':
          handleZoomOut()
          break
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [selectedImageIndex])

  // Determine the grid columns class based on the columns prop
  const getGridColumnsClass = () => {
    switch (columns) {
      case 1:
        return "grid-cols-1"
      case 2:
        return "grid-cols-1 sm:grid-cols-2"
      case 3:
        return "grid-cols-2 sm:grid-cols-3"
      case 5:
        return "grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5"
      case 6:
        return "grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6"
      case 4:
      default:
        return "grid-cols-2 sm:grid-cols-3 md:grid-cols-4"
    }
  }

  // Get masonry columns class
  const getMasonryColumnsClass = () => {
    switch (columns) {
      case 1:
        return "columns-1"
      case 2:
        return "columns-1 sm:columns-2"
      case 3:
        return "columns-2 sm:columns-3"
      case 5:
        return "columns-2 sm:columns-3 md:columns-4 lg:columns-5"
      case 6:
        return "columns-2 sm:columns-3 md:columns-4 lg:columns-6"
      case 4:
      default:
        return "columns-2 sm:columns-3 md:columns-4"
    }
  }

  if (images.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-8 border-2 border-dashed border-muted-foreground/30 rounded-lg">
        <div className="text-muted-foreground text-center">
          <LayoutGrid className="h-12 w-12 mx-auto mb-2" />
          <p className="text-sm">Aucune image à afficher</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Gallery Grid */}
      <div ref={galleryRef}>
        {layout === "masonry" ? (
          <div className={`${getMasonryColumnsClass()} gap-4 space-y-4`}>
            {images.map((image, index) => (
              <div
                key={index}
                className="break-inside-avoid mb-4 group cursor-pointer"
                onClick={() => openLightbox(index)}
              >
                <div className="relative overflow-hidden rounded-lg bg-muted/30">
                  {!loadedImages.has(index) && (
                    <div className="absolute inset-0 flex items-center justify-center z-10">
                      <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                    </div>
                  )}
                  <img
                    src={image.url}
                    alt={image.altText || `Image ${index + 1}`}
                    className="w-full object-cover transition-transform duration-300 group-hover:scale-105"
                    onLoad={() => handleImageLoad(index)}
                    loading={enableLazyLoading ? "lazy" : "eager"}
                  />
                  {image.caption && (
                    <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white p-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <p className="text-xs truncate">{image.caption}</p>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className={`grid ${getGridColumnsClass()} gap-4`}>
            {images.map((image, index) => (
              <div
                key={index}
                className="relative aspect-square overflow-hidden rounded-lg cursor-pointer group bg-muted/30"
                onClick={() => openLightbox(index)}
              >
                {!loadedImages.has(index) && (
                  <div className="absolute inset-0 flex items-center justify-center z-10">
                    <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                  </div>
                )}
                <img
                  src={image.url}
                  alt={image.altText || `Image ${index + 1}`}
                  className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                  onLoad={() => handleImageLoad(index)}
                  loading={enableLazyLoading ? "lazy" : "eager"}
                />
                {image.caption && (
                  <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white p-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <p className="text-xs truncate">{image.caption}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Lightbox Dialog */}
      <Dialog open={selectedImageIndex !== null} onOpenChange={(open) => !open && closeLightbox()}>
        <DialogContent className="max-w-screen-xl w-[95vw] h-[95vh] p-0 border-none bg-black/95">
          {selectedImageIndex !== null && (
            <div className="relative w-full h-full flex items-center justify-center">
              {/* Main Image */}
              <img
                src={images[selectedImageIndex].url}
                alt={images[selectedImageIndex].altText || `Image ${selectedImageIndex + 1}`}
                className="max-w-full max-h-full object-contain transition-transform duration-300"
                style={{
                  transform: `scale(${zoom})`,
                }}
              />

              {/* Close Button */}
              <Button
                size="icon"
                variant="ghost"
                className="absolute top-4 right-4 text-white hover:bg-white/20"
                onClick={closeLightbox}
                title="Fermer (Échap)"
              >
                <X className="h-6 w-6" />
              </Button>

              {/* Navigation Buttons */}
              {images.length > 1 && (
                <>
                  <Button
                    size="icon"
                    variant="ghost"
                    className="absolute left-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20"
                    onClick={goToPrevious}
                    title="Image précédente (←)"
                  >
                    <ChevronLeft className="h-8 w-8" />
                  </Button>
                  <Button
                    size="icon"
                    variant="ghost"
                    className="absolute right-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20"
                    onClick={goToNext}
                    title="Image suivante (→)"
                  >
                    <ChevronRight className="h-8 w-8" />
                  </Button>
                </>
              )}

              {/* Zoom Controls */}
              <div className="absolute top-4 left-4 flex gap-2">
                <Button
                  size="sm"
                  variant="ghost"
                  className="text-white hover:bg-white/20"
                  onClick={handleZoomOut}
                  disabled={zoom <= 0.5}
                  title="Zoom arrière (-)"
                >
                  <ZoomOut className="h-4 w-4" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  className="text-white hover:bg-white/20"
                  onClick={handleZoomIn}
                  disabled={zoom >= 3}
                  title="Zoom avant (+)"
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>
              </div>

              {/* Action Buttons */}
              <div className="absolute bottom-4 left-4 flex gap-2">
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={handleDownload}
                  disabled={isDownloading}
                  title="Télécharger"
                >
                  {isDownloading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Download className="h-4 w-4" />}
                </Button>
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={handleShare}
                  disabled={isSharing}
                  title="Partager"
                >
                  {isSharing ? <Loader2 className="h-4 w-4 animate-spin" /> : <Share2 className="h-4 w-4" />}
                </Button>
              </div>

              {/* Image Counter and Info */}
              <div className="absolute bottom-4 right-4 flex flex-col items-end gap-2">
                <Badge variant="secondary" className="bg-black/60 text-white border-none">
                  {selectedImageIndex + 1} / {images.length}
                </Badge>
                {zoom !== 1 && (
                  <Badge variant="secondary" className="bg-black/60 text-white border-none">
                    {Math.round(zoom * 100)}%
                  </Badge>
                )}
              </div>

              {/* Image Caption */}
              {images[selectedImageIndex].caption && (
                <div className="absolute bottom-4 left-1/2 -translate-x-1/2 max-w-md">
                  <div className="bg-black/60 text-white px-4 py-2 rounded-lg text-center">
                    <p className="text-sm">{images[selectedImageIndex].caption}</p>
                  </div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
