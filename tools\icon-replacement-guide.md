# Guide de remplacement des icônes ACR Direct

## 📁 Fichiers créés et à remplacer

J'ai créé tous les fichiers placeholder nécessaires. Voici comment les remplacer avec vos vraies icônes :

### 🍎 Apple Touch Icons (pour iOS "Ajouter à l'écran d'accueil")

Ces icônes doivent avoir le logo ACR Direct **plus grand** avec **moins de padding** que les icônes Android :

| Fichier | Taille | Logo recommandé | Padding |
|---------|--------|-----------------|---------|
| `apple-touch-icon-57x57.png` | 57x57px | ~45x45px | 6px |
| `apple-touch-icon-60x60.png` | 60x60px | ~48x48px | 6px |
| `apple-touch-icon-72x72.png` | 72x72px | ~56x56px | 8px |
| `apple-touch-icon-76x76.png` | 76x76px | ~60x60px | 8px |
| `apple-touch-icon-114x114.png` | 114x114px | ~90x90px | 12px |
| `apple-touch-icon-120x120.png` | 120x120px | ~96x96px | 12px |
| `apple-touch-icon-144x144.png` | 144x144px | ~114x114px | 15px |
| `apple-touch-icon-152x152.png` | 152x152px | ~120x120px | 16px |
| `apple-touch-icon-180x180.png` | 180x180px | ~140x140px | 20px |

### 🤖 Icônes Maskable (pour Android adaptatif)

Ces icônes doivent avoir le logo dans la "zone sûre" (80% du canvas) :

| Fichier | Taille | Zone sûre | Logo max | Margin |
|---------|--------|-----------|----------|--------|
| `icon-maskable-192x192.png` | 192x192px | 154x154px | ~120x120px | 19px |
| `icon-maskable-512x512.png` | 512x512px | 410x410px | ~320x320px | 51px |

## 🎨 Instructions de création

### Pour les Apple Touch Icons :
1. **Prenez votre logo ACR Direct** (C avec flèche bleue)
2. **Créez un canvas blanc** de la taille requise
3. **Centrez le logo** en utilisant les dimensions recommandées ci-dessus
4. **Le logo doit être plus visible** que dans les icônes Android actuelles
5. **Exportez en PNG** avec le nom exact du fichier

### Pour les icônes Maskable :
1. **Créez un canvas blanc** de la taille requise
2. **Placez le logo au centre** dans la zone sûre
3. **Le logo ne doit pas dépasser** la zone sûre (80% du canvas)
4. **Testez sur** https://maskable.app/ pour vérifier l'apparence

## 🔄 Remplacement des fichiers

### Méthode 1 : Copier-coller
1. Ouvrez le dossier `public/` de votre projet
2. Remplacez chaque fichier placeholder par votre vraie icône
3. **Gardez exactement le même nom de fichier**

### Méthode 2 : Glisser-déposer
1. Sélectionnez vos icônes créées
2. Glissez-les dans le dossier `public/`
3. Confirmez le remplacement

## ✅ Vérification

Après remplacement :

1. **Ouvrez** `/admin/diagnostics` → onglet PWA
2. **Cliquez** sur "Vérifier les icônes"
3. **Vérifiez** que toutes les icônes apparaissent comme "Disponible"
4. **Testez** l'installation sur différents appareils

## 📊 Résultat attendu

Après remplacement de tous les fichiers :

- ✅ **Installation native PWA** : Fonctionne parfaitement
- ✅ **"Ajouter à l'écran d'accueil" iOS** : Logo bien proportionné
- ✅ **"Ajouter à l'écran d'accueil" Android** : Affichage correct
- ✅ **Icônes adaptatives Android** : Logo visible dans toutes les formes

## 🎯 Priorité de remplacement

1. **Priorité 1** : `apple-touch-icon-180x180.png` (iOS principal)
2. **Priorité 2** : `icon-maskable-192x192.png` et `icon-maskable-512x512.png`
3. **Priorité 3** : Autres tailles Apple Touch Icons

## 💡 Conseils

- **Utilisez la même palette de couleurs** : gris clair et bleu ACR
- **Gardez la cohérence** du design entre toutes les tailles
- **Testez sur vrais appareils** après remplacement
- **Utilisez les outils de diagnostic** pour valider

---

**Note** : Les fichiers placeholder actuels permettent au système de fonctionner sans erreur en attendant vos vraies icônes.
