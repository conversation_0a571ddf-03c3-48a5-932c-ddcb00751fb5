"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON>, Di<PERSON>Content, DialogTrigger } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Download, Share2, X, Loader2, ZoomIn, ZoomOut, RotateCw, Maximize2 } from "lucide-react"
import { useIsMobile } from "@/hooks/use-mobile"
import { OptimizedImage } from "@/components/optimized-image"

interface SingleImageViewerProps {
  imageUrl: string
  caption?: string
  altText?: string
  showControls?: boolean
  fullViewport?: boolean
}

export function SingleImageViewer({
  imageUrl,
  caption,
  altText,
  showControls = true,
  fullViewport = true
}: SingleImageViewerProps) {
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [isSharing, setIsSharing] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [zoom, setZoom] = useState(1)
  const [rotation, setRotation] = useState(0)
  const [imageError, setImageError] = useState(false)
  const [imageDimensions, setImageDimensions] = useState({ width: 0, height: 0 })
  const imageRef = useRef<HTMLImageElement>(null)
  const isMobile = useIsMobile()
  const [isDownloading, setIsDownloading] = useState(false)

  // Get container styles for optimal viewport display
  const getContainerStyles = () => {
    if (!fullViewport) {
      return {
        maxHeight: "60vh",
        maxWidth: "100%"
      }
    }

    // For full viewport, constrain by height to prevent portrait images from being too large
    // Reserve space for header/navigation and padding
    return {
      maxHeight: "calc(100vh - 120px)",
      maxWidth: "100%",
      // Don't force width, let the image determine its own width based on aspect ratio
      height: "auto"
    }
  }

  // Handle image load
  const handleImageLoad = (event: React.SyntheticEvent<HTMLImageElement>) => {
    const img = event.currentTarget
    setImageDimensions({ width: img.naturalWidth, height: img.naturalHeight })
    setIsLoading(false)
    setImageError(false)
  }

  // Handle image error
  const handleImageError = () => {
    setIsLoading(false)
    setImageError(true)
  }

  // Zoom controls
  const handleZoomIn = () => setZoom(prev => Math.min(prev + 0.25, 3))
  const handleZoomOut = () => setZoom(prev => Math.max(prev - 0.25, 0.5))
  const handleRotate = () => setRotation(prev => (prev + 90) % 360)
  const handleResetView = () => {
    setZoom(1)
    setRotation(0)
  }

  // Fonction pour obtenir l'URL proxifiée
  const getProxyUrl = (url: string) => {
    // Encoder l'URL pour éviter les problèmes avec les caractères spéciaux
    const encodedUrl = encodeURIComponent(url)
    return `/api/proxy-image?url=${encodedUrl}`
  }

  const handleDownload = async () => {
    try {
      setIsDownloading(true)

      // Utiliser l'URL d'origine pour le téléchargement direct
      const fileName = caption ? `${caption}.jpg` : "image.jpg"
      const link = document.createElement("a")
      link.href = imageUrl
      link.download = fileName
      link.target = "_blank"
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } catch (error) {
      console.error("Erreur lors du téléchargement de l'image:", error)
      window.open(imageUrl, "_blank")
    } finally {
      setIsDownloading(false)
    }
  }

  const handleShare = async () => {
    try {
      setIsSharing(true)

      // Utiliser l'URL proxifiée pour contourner CORS
      const proxyUrl = getProxyUrl(imageUrl)

      // Récupérer l'image via le proxy
      const response = await fetch(proxyUrl)

      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`)
      }

      const blob = await response.blob()
      const fileName = caption ? `${caption}.jpg` : "image.jpg"
      const file = new File([blob], fileName, { type: blob.type || "image/jpeg" })

      // Vérifier si le navigateur supporte le partage de fichiers
      if (navigator.share && navigator.canShare && navigator.canShare({ files: [file] })) {
        await navigator.share({
          files: [file],
          title: caption || "Image partagée",
          text: caption || "Regardez cette image",
        })
      } else {
        // Fallback pour les navigateurs qui ne supportent pas le partage de fichiers
        if (navigator.share) {
          // Partager au moins l'URL si le partage de fichiers n'est pas supporté
          await navigator.share({
            title: caption || "Image partagée",
            text: caption || "Regardez cette image",
            url: imageUrl,
          })
        } else {
          // Fallback pour les navigateurs sans API Web Share
          await navigator.clipboard.writeText(imageUrl)
          alert(
            "L'URL de l'image a été copiée dans le presse-papier car votre navigateur ne supporte pas le partage direct.",
          )
        }
      }
    } catch (error) {
      console.error("Erreur lors du partage de l'image:", error)

      // Tenter de partager l'URL comme solution de repli
      try {
        if (navigator.share) {
          await navigator.share({
            title: caption || "Image partagée",
            text: caption || "Regardez cette image",
            url: imageUrl,
          })
        } else {
          await navigator.clipboard.writeText(imageUrl)
          alert(
            "L'URL de l'image a été copiée dans le presse-papier car votre navigateur ne supporte pas le partage direct.",
          )
        }
      } catch (shareError) {
        console.error("Erreur lors du partage de l'URL:", shareError)
        alert("Impossible de partager l'image. Veuillez essayer de la télécharger à la place.")
      }
    } finally {
      setIsSharing(false)
    }
  }

  const containerStyles = getContainerStyles()

  if (imageError) {
    return (
      <div className="flex flex-col items-center justify-center p-8 border-2 border-dashed border-muted-foreground/30 rounded-lg">
        <div className="text-muted-foreground text-center">
          <X className="h-12 w-12 mx-auto mb-2" />
          <p className="text-sm">Impossible de charger l'image</p>
          {caption && <p className="text-xs mt-1">{caption}</p>}
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col w-full">
      {/* Image Container */}
      <div className="relative group">
        {/* Loading State */}
        {isLoading && (
          <div
            className="flex items-center justify-center bg-muted/30 rounded-lg"
            style={containerStyles}
          >
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        )}

        {/* Controls Overlay */}
        {showControls && !isLoading && (
          <div className="absolute top-4 right-4 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <div className="flex gap-2 bg-black/60 backdrop-blur-sm rounded-lg p-2">
              <Button
                size="sm"
                variant="ghost"
                className="text-white hover:bg-white/20 h-8 w-8 p-0"
                onClick={handleDownload}
                disabled={isDownloading}
                title="Télécharger"
              >
                {isDownloading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Download className="h-4 w-4" />}
              </Button>
              <Button
                size="sm"
                variant="ghost"
                className="text-white hover:bg-white/20 h-8 w-8 p-0"
                onClick={handleShare}
                disabled={isSharing}
                title="Partager"
              >
                {isSharing ? <Loader2 className="h-4 w-4 animate-spin" /> : <Share2 className="h-4 w-4" />}
              </Button>
              <Button
                size="sm"
                variant="ghost"
                className="text-white hover:bg-white/20 h-8 w-8 p-0"
                onClick={() => setIsFullscreen(true)}
                title="Plein écran"
              >
                <Maximize2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}

        {/* Image Info Badge */}
        {imageDimensions.width > 0 && !isLoading && (
          <div className="absolute top-4 left-4 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <Badge variant="secondary" className="bg-black/60 text-white border-none">
              {imageDimensions.width} × {imageDimensions.height}
            </Badge>
          </div>
        )}

        {/* Main Image */}
        <Dialog open={isFullscreen} onOpenChange={setIsFullscreen}>
          <DialogTrigger asChild>
            <div className="relative cursor-pointer flex items-center justify-center w-full">
              <OptimizedImage
                ref={imageRef}
                src={imageUrl}
                alt={altText || caption || "Image"}
                className="rounded-lg transition-transform duration-300 hover:scale-[1.02] object-contain"
                style={containerStyles}
                onLoad={handleImageLoad}
                onError={handleImageError}
                loading="lazy"
                sizes={`(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw`}
              />
            </div>
          </DialogTrigger>

          {/* Fullscreen Dialog */}
          <DialogContent className="max-w-screen-xl w-[95vw] h-[95vh] p-0 border-none bg-black/95">
            <div className="relative w-full h-full flex items-center justify-center">
              {/* Fullscreen Image */}
              <img
                src={imageUrl}
                alt={altText || caption || "Image"}
                className="max-w-[95vw] max-h-[95vh] object-contain transition-transform duration-300"
                style={{
                  transform: `scale(${zoom}) rotate(${rotation}deg)`,
                }}
              />

              {/* Fullscreen Controls */}
              <div className="absolute top-4 right-4 flex gap-2">
                <Button
                  size="icon"
                  variant="ghost"
                  className="text-white hover:bg-white/20"
                  onClick={() => setIsFullscreen(false)}
                  title="Fermer"
                >
                  <X className="h-6 w-6" />
                </Button>
              </div>

              <div className="absolute top-4 left-4 flex gap-2">
                <Button
                  size="sm"
                  variant="ghost"
                  className="text-white hover:bg-white/20"
                  onClick={handleZoomOut}
                  disabled={zoom <= 0.5}
                  title="Zoom arrière"
                >
                  <ZoomOut className="h-4 w-4" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  className="text-white hover:bg-white/20"
                  onClick={handleZoomIn}
                  disabled={zoom >= 3}
                  title="Zoom avant"
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  className="text-white hover:bg-white/20"
                  onClick={handleRotate}
                  title="Rotation"
                >
                  <RotateCw className="h-4 w-4" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  className="text-white hover:bg-white/20"
                  onClick={handleResetView}
                  title="Réinitialiser"
                >
                  Réinitialiser
                </Button>
              </div>

              <div className="absolute bottom-4 left-4 flex gap-2">
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={handleDownload}
                  disabled={isDownloading}
                  title="Télécharger"
                >
                  {isDownloading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Download className="h-4 w-4" />}
                </Button>
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={handleShare}
                  disabled={isSharing}
                  title="Partager"
                >
                  {isSharing ? <Loader2 className="h-4 w-4 animate-spin" /> : <Share2 className="h-4 w-4" />}
                </Button>
              </div>

              {/* Zoom indicator */}
              {zoom !== 1 && (
                <div className="absolute bottom-4 right-4">
                  <Badge variant="secondary" className="bg-black/60 text-white border-none">
                    {Math.round(zoom * 100)}%
                  </Badge>
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Caption */}
      {caption && (
        <div className="mt-4 text-center">
          <p className="text-sm text-muted-foreground leading-relaxed">{caption}</p>
        </div>
      )}
    </div>
  )
}
