"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ChevronLeft, ChevronRight, Play, Pause, Maximize2, Download, Share2, Loader2 } from "lucide-react"
import { useIsMobile } from "@/hooks/use-mobile"
import { OptimizedImage } from "@/components/optimized-image"
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog"

interface CarouselSlide {
  url: string
  title?: string
  description?: string
  altText?: string
}

interface CarouselViewerProps {
  slides: CarouselSlide[]
  autoplay?: boolean
  interval?: number
  showControls?: boolean
  showIndicators?: boolean
  enableFullscreen?: boolean
  aspectRatio?: "16/9" | "4/3" | "1/1" | "auto"
}

export function CarouselViewer({
  slides,
  autoplay = false,
  interval = 5,
  showControls = true,
  showIndicators = true,
  enableFullscreen = true,
  aspectRatio = "16/9"
}: CarouselViewerProps) {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isPaused, setIsPaused] = useState(false)
  const [isPlaying, setIsPlaying] = useState(autoplay)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [loadedSlides, setLoadedSlides] = useState<Set<number>>(new Set())
  const [isDownloading, setIsDownloading] = useState(false)
  const [isSharing, setIsSharing] = useState(false)
  const carouselRef = useRef<HTMLDivElement>(null)
  const isMobile = useIsMobile()
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  // Navigation functions
  const goToPrevious = useCallback(() => {
    setCurrentSlide((prev) => (prev === 0 ? slides.length - 1 : prev - 1))
  }, [slides.length])

  const goToNext = useCallback(() => {
    setCurrentSlide((prev) => (prev === slides.length - 1 ? 0 : prev + 1))
  }, [slides.length])

  const goToSlide = useCallback((index: number) => {
    setCurrentSlide(index)
  }, [])

  // Play/pause controls
  const togglePlayPause = () => {
    setIsPlaying(!isPlaying)
  }

  // Handle slide loading
  const handleSlideLoad = useCallback((index: number) => {
    setLoadedSlides(prev => new Set([...prev, index]))
  }, [])

  // Download and share functions
  const handleDownload = async () => {
    setIsDownloading(true)
    try {
      const slide = slides[currentSlide]
      const response = await fetch(slide.url)
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `slide-${currentSlide + 1}.jpg`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Erreur lors du téléchargement:', error)
    } finally {
      setIsDownloading(false)
    }
  }

  const handleShare = async () => {
    setIsSharing(true)
    try {
      const slide = slides[currentSlide]
      if (navigator.share) {
        await navigator.share({
          title: slide.title || `Diapositive ${currentSlide + 1}`,
          text: slide.description || slide.title || 'Diapositive partagée',
          url: slide.url,
        })
      } else {
        await navigator.clipboard.writeText(slide.url)
        alert('URL copiée dans le presse-papier')
      }
    } catch (error) {
      console.error('Erreur lors du partage:', error)
    } finally {
      setIsSharing(false)
    }
  }

  // Get aspect ratio class
  const getAspectRatioClass = () => {
    switch (aspectRatio) {
      case "4/3":
        return "aspect-[4/3]"
      case "1/1":
        return "aspect-square"
      case "auto":
        return ""
      case "16/9":
      default:
        return "aspect-[16/9]"
    }
  }

  // Handle autoplay
  useEffect(() => {
    if (isPlaying && !isPaused && slides.length > 1) {
      intervalRef.current = setInterval(() => {
        goToNext()
      }, interval * 1000)
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [isPlaying, isPaused, currentSlide, interval, slides.length, goToNext])

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'ArrowLeft':
          goToPrevious()
          break
        case 'ArrowRight':
          goToNext()
          break
        case ' ':
          event.preventDefault()
          togglePlayPause()
          break
        case 'Escape':
          if (isFullscreen) setIsFullscreen(false)
          break
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [goToPrevious, goToNext, isFullscreen])

  // Pause on hover
  const handleMouseEnter = () => {
    if (!isMobile) setIsPaused(true)
  }

  const handleMouseLeave = () => {
    if (!isMobile) setIsPaused(false)
  }

  if (slides.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-8 border-2 border-dashed border-muted-foreground/30 rounded-lg">
        <div className="text-muted-foreground text-center">
          <ChevronRight className="h-12 w-12 mx-auto mb-2" />
          <p className="text-sm">Aucune diapositive à afficher</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Carousel Header */}
      <div className="flex items-center justify-between">
        <Badge variant="outline" className="text-sm">
          {slides.length} diapositive{slides.length > 1 ? 's' : ''}
        </Badge>

        {showControls && slides.length > 1 && (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={togglePlayPause}
              className="h-8"
            >
              {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              <span className="ml-1 hidden sm:inline">
                {isPlaying ? 'Pause' : 'Lecture'}
              </span>
            </Button>

            {enableFullscreen && (
              <Dialog open={isFullscreen} onOpenChange={setIsFullscreen}>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm" className="h-8">
                    <Maximize2 className="h-4 w-4" />
                    <span className="ml-1 hidden sm:inline">Plein écran</span>
                  </Button>
                </DialogTrigger>
              </Dialog>
            )}
          </div>
        )}
      </div>

      {/* Main Carousel */}
      <div
        ref={carouselRef}
        className="relative w-full group"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <div className="overflow-hidden rounded-lg bg-muted/30">
          <div
            className="flex transition-transform duration-500 ease-in-out"
            style={{ transform: `translateX(-${currentSlide * 100}%)` }}
          >
            {slides.map((slide, index) => (
              <div key={index} className="w-full flex-shrink-0">
                <div className={`relative ${getAspectRatioClass()}`}>
                  {!loadedSlides.has(index) && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                    </div>
                  )}

                  <OptimizedImage
                    src={slide.url}
                    alt={slide.altText || slide.title || `Diapositive ${index + 1}`}
                    className="w-full h-full object-cover"
                    onLoad={() => handleSlideLoad(index)}
                    loading={index === 0 ? "eager" : "lazy"}
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
                  />

                  {/* Slide Content Overlay */}
                  {(slide.title || slide.description) && (
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent text-white p-4 sm:p-6">
                      {slide.title && (
                        <h3 className="text-lg sm:text-xl font-semibold mb-1">{slide.title}</h3>
                      )}
                      {slide.description && (
                        <p className="text-sm sm:text-base opacity-90 line-clamp-2">{slide.description}</p>
                      )}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Navigation Controls */}
        {showControls && slides.length > 1 && (
          <>
            <Button
              size="icon"
              variant="secondary"
              className="absolute left-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-black/60 hover:bg-black/80 text-white border-none"
              onClick={goToPrevious}
              title="Diapositive précédente"
            >
              <ChevronLeft className="h-6 w-6" />
            </Button>

            <Button
              size="icon"
              variant="secondary"
              className="absolute right-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-black/60 hover:bg-black/80 text-white border-none"
              onClick={goToNext}
              title="Diapositive suivante"
            >
              <ChevronRight className="h-6 w-6" />
            </Button>
          </>
        )}

        {/* Action Controls */}
        {showControls && (
          <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <div className="flex gap-2 bg-black/60 backdrop-blur-sm rounded-lg p-2">
              <Button
                size="sm"
                variant="ghost"
                className="text-white hover:bg-white/20 h-8 w-8 p-0"
                onClick={handleDownload}
                disabled={isDownloading}
                title="Télécharger"
              >
                {isDownloading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Download className="h-4 w-4" />}
              </Button>

              <Button
                size="sm"
                variant="ghost"
                className="text-white hover:bg-white/20 h-8 w-8 p-0"
                onClick={handleShare}
                disabled={isSharing}
                title="Partager"
              >
                {isSharing ? <Loader2 className="h-4 w-4 animate-spin" /> : <Share2 className="h-4 w-4" />}
              </Button>

              {enableFullscreen && (
                <Button
                  size="sm"
                  variant="ghost"
                  className="text-white hover:bg-white/20 h-8 w-8 p-0"
                  onClick={() => setIsFullscreen(true)}
                  title="Plein écran"
                >
                  <Maximize2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        )}

        {/* Slide Counter */}
        <div className="absolute top-4 left-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <Badge variant="secondary" className="bg-black/60 text-white border-none">
            {currentSlide + 1} / {slides.length}
          </Badge>
        </div>

        {/* Indicators */}
        {showIndicators && slides.length > 1 && (
          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2">
            {slides.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full transition-all duration-200 ${
                  index === currentSlide
                    ? "bg-white scale-125"
                    : "bg-white/50 hover:bg-white/75"
                }`}
                onClick={() => goToSlide(index)}
                aria-label={`Aller à la diapositive ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>

      {/* Fullscreen Dialog */}
      {enableFullscreen && (
        <DialogContent className="max-w-screen-xl w-[95vw] h-[95vh] p-0 border-none bg-black/95">
          <div className="relative w-full h-full flex items-center justify-center">
            <div className="relative w-full h-full">
              <OptimizedImage
                src={slides[currentSlide].url}
                alt={slides[currentSlide].altText || slides[currentSlide].title || `Diapositive ${currentSlide + 1}`}
                className="w-full h-full object-contain"
                sizes="100vw"
              />

              {/* Fullscreen Controls */}
              <div className="absolute top-4 right-4 flex gap-2">
                <Button
                  size="icon"
                  variant="ghost"
                  className="text-white hover:bg-white/20"
                  onClick={() => setIsFullscreen(false)}
                  title="Fermer"
                >
                  <ChevronRight className="h-6 w-6 rotate-45" />
                </Button>
              </div>

              {slides.length > 1 && (
                <>
                  <Button
                    size="icon"
                    variant="ghost"
                    className="absolute left-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20"
                    onClick={goToPrevious}
                    title="Diapositive précédente"
                  >
                    <ChevronLeft className="h-8 w-8" />
                  </Button>

                  <Button
                    size="icon"
                    variant="ghost"
                    className="absolute right-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20"
                    onClick={goToNext}
                    title="Diapositive suivante"
                  >
                    <ChevronRight className="h-8 w-8" />
                  </Button>
                </>
              )}

              <div className="absolute bottom-4 left-1/2 -translate-x-1/2">
                <Badge variant="secondary" className="bg-black/60 text-white border-none">
                  {currentSlide + 1} / {slides.length}
                </Badge>
              </div>
            </div>
          </div>
        </DialogContent>
      )}
    </div>
  )
}
