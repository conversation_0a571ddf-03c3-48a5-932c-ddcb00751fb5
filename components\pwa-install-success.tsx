"use client"

import { useState, useEffect } from "react"
import { CheckCircle, X, Smartphone, Monitor } from "lucide-react"
import { usePWAInstall } from "@/lib/hooks/use-pwa-install"

export function PWAInstallSuccess() {
  const { capabilities, isInstalled } = usePWAInstall()
  const [showSuccess, setShowSuccess] = useState(false)
  const [justInstalled, setJustInstalled] = useState(false)

  useEffect(() => {
    // Vérifier si l'installation vient de se faire
    const checkInstallation = () => {
      const wasInstalled = localStorage.getItem("pwa-was-installed") === "true"
      
      if (isInstalled && !wasInstalled) {
        // L'app vient d'être installée
        setJustInstalled(true)
        setShowSuccess(true)
        localStorage.setItem("pwa-was-installed", "true")
        
        // Masquer automatiquement après 10 secondes
        setTimeout(() => {
          setShowSuccess(false)
        }, 10000)
      } else if (isInstalled) {
        localStorage.setItem("pwa-was-installed", "true")
      }
    }

    checkInstallation()
  }, [isInstalled])

  const handleDismiss = () => {
    setShowSuccess(false)
  }

  const getSuccessMessage = () => {
    if (capabilities.platform === "ios") {
      return "ACR Direct a été ajoutée à votre écran d'accueil ! Vous pouvez maintenant y accéder directement depuis votre écran d'accueil."
    } else {
      return "ACR Direct a été installée avec succès ! L'application est maintenant disponible dans votre menu d'applications."
    }
  }

  const getSuccessIcon = () => {
    if (capabilities.platform === "ios" || capabilities.platform === "android") {
      return <Smartphone className="h-8 w-8 text-green-500" />
    } else {
      return <Monitor className="h-8 w-8 text-green-500" />
    }
  }

  if (!showSuccess || !justInstalled) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="relative w-full max-w-md mx-4 rounded-2xl bg-white p-6 shadow-2xl dark:bg-gray-800 animate-in fade-in zoom-in duration-300">
        <button
          onClick={handleDismiss}
          className="absolute right-4 top-4 rounded-full p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-200 transition-colors"
        >
          <X size={20} />
        </button>

        <div className="text-center">
          {/* Icône de succès */}
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/20">
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>

          {/* Titre */}
          <h2 className="mb-2 text-xl font-bold text-gray-900 dark:text-white">
            Installation réussie !
          </h2>

          {/* Message */}
          <p className="mb-6 text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
            {getSuccessMessage()}
          </p>

          {/* Icône de plateforme */}
          <div className="mb-6 flex justify-center">
            {getSuccessIcon()}
          </div>

          {/* Instructions spécifiques */}
          <div className="mb-6 rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20">
            <h3 className="mb-2 text-sm font-semibold text-blue-900 dark:text-blue-100">
              Comment accéder à l'application :
            </h3>
            <ul className="text-xs text-blue-800 dark:text-blue-200 space-y-1">
              {capabilities.platform === "ios" ? (
                <>
                  <li>• Recherchez l'icône "ACR Direct" sur votre écran d'accueil</li>
                  <li>• Appuyez dessus pour ouvrir l'application</li>
                  <li>• L'application s'ouvrira en mode plein écran</li>
                </>
              ) : capabilities.platform === "windows" ? (
                <>
                  <li>• Recherchez "ACR Direct" dans le menu Démarrer</li>
                  <li>• Ou trouvez l'icône sur votre bureau</li>
                  <li>• Double-cliquez pour ouvrir l'application</li>
                </>
              ) : capabilities.platform === "android" ? (
                <>
                  <li>• Recherchez l'icône "ACR Direct" dans vos applications</li>
                  <li>• Appuyez dessus pour ouvrir l'application</li>
                  <li>• Vous pouvez aussi l'ajouter à votre écran d'accueil</li>
                </>
              ) : (
                <>
                  <li>• Recherchez "ACR Direct" dans vos applications</li>
                  <li>• Cliquez dessus pour ouvrir l'application</li>
                  <li>• L'application s'ouvrira comme une application native</li>
                </>
              )}
            </ul>
          </div>

          {/* Bouton de fermeture */}
          <button
            onClick={handleDismiss}
            className="w-full rounded-lg bg-blue-600 px-4 py-3 text-sm font-semibold text-white hover:bg-blue-700 transition-colors duration-200"
          >
            Parfait, j'ai compris !
          </button>
        </div>
      </div>
    </div>
  )
}
